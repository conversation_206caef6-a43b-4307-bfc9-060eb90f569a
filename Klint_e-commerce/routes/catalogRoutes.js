const express = require("express");
const router = express.Router();
const {
  getProductsForCatalog,
  getCategories,
  getProductById,
  getProductsForComparison,
  getProductsByCategory,
} = require("../queries");

// Get all categories
router.get("/categories", async (req, res) => {
  try {
    const categories = await getCategories();
    res.json({
      success: true,
      categories,
    });
  } catch (error) {
    console.error("Error fetching categories:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching categories",
      error: error.message,
    });
  }
});

// Get all active products
router.get("/products", async (req, res) => {
  try {
    const products = await getProductsForCatalog();
    res.json({
      success: true,
      products,
    });
  } catch (error) {
    console.error("Error fetching products:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching products",
      error: error.message,
    });
  }
});

// Get a single product by ID
router.get("/products/:id", async (req, res) => {
  try {
    const product = await getProductById(req.params.id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    res.json({
      success: true,
      product,
    });
  } catch (error) {
    console.error("Error fetching product:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching product",
      error: error.message,
    });
  }
});

// Compare product prices across vendors
router.get("/compare", async (req, res) => {
  try {
    const { product_name } = req.query;

    if (!product_name) {
      return res.status(400).json({
        success: false,
        message: "Product name is required for comparison",
      });
    }

    const products = await getProductsForComparison(product_name);

    if (products.length === 0) {
      return res.status(404).json({
        success: false,
        message: "No products found for comparison",
      });
    }

    res.json({
      success: true,
      count: products.length,
      products,
    });
  } catch (error) {
    console.error("Error comparing product prices:", error);
    res.status(500).json({
      success: false,
      message: "Error comparing product prices",
      error: error.message,
    });
  }
});

// Get products by category
router.get("/categories/:categoryId/products", async (req, res) => {
  try {
    const categoryId = parseInt(req.params.categoryId);

    if (isNaN(categoryId)) {
      return res.status(400).json({
        success: false,
        message: "Invalid category ID",
      });
    }

    const products = await getProductsByCategory(categoryId);

    res.json({
      success: true,
      count: products.length,
      products,
    });
  } catch (error) {
    console.error("Error fetching products by category:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching products by category",
      error: error.message,
    });
  }
});

module.exports = router;
