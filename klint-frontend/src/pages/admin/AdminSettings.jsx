import React, { useState } from 'react';

function AdminSettings() {
  const [activeTab, setActiveTab] = useState('general');
  const [settings, setSettings] = useState({
    siteName: 'Klint E-commerce',
    siteDescription: 'Your trusted online marketplace',
    contactEmail: '<EMAIL>',
    supportEmail: '<EMAIL>',
    maintenanceMode: false,
    allowRegistration: true,
    requireEmailVerification: true,
    maxFileSize: '5',
    allowedFileTypes: 'jpg,jpeg,png,gif,webp',
  });

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handleSaveSettings = (e) => {
    e.preventDefault();
    // TODO: Implement API call to save settings
    console.log('Saving settings:', settings);
    alert('Settings saved successfully!');
  };

  const tabs = [
    { id: 'general', name: 'General', icon: '⚙️' },
    { id: 'users', name: 'User Settings', icon: '👥' },
    { id: 'uploads', name: 'File Uploads', icon: '📁' },
    { id: 'email', name: 'Email Settings', icon: '📧' },
    { id: 'maintenance', name: 'Maintenance', icon: '🔧' },
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Site Settings</h1>
        <p className="mt-1 text-sm text-gray-500">
          Manage your platform's configuration and settings.
        </p>
      </div>

      <div className="bg-white shadow rounded-lg">
        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-klintblue text-klintblue'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
              >
                <span>{tab.icon}</span>
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          <form onSubmit={handleSaveSettings}>
            {/* General Settings */}
            {activeTab === 'general' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">General Settings</h3>
                
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Site Name
                    </label>
                    <input
                      type="text"
                      name="siteName"
                      value={settings.siteName}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-klintblue"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Contact Email
                    </label>
                    <input
                      type="email"
                      name="contactEmail"
                      value={settings.contactEmail}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-klintblue"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Site Description
                  </label>
                  <textarea
                    name="siteDescription"
                    rows="3"
                    value={settings.siteDescription}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-klintblue"
                  />
                </div>
              </div>
            )}

            {/* User Settings */}
            {activeTab === 'users' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">User Settings</h3>
                
                <div className="space-y-4">
                  <div className="flex items-center">
                    <input
                      id="allowRegistration"
                      name="allowRegistration"
                      type="checkbox"
                      checked={settings.allowRegistration}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-klintblue focus:ring-klintblue border-gray-300 rounded"
                    />
                    <label htmlFor="allowRegistration" className="ml-2 block text-sm text-gray-900">
                      Allow new user registration
                    </label>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      id="requireEmailVerification"
                      name="requireEmailVerification"
                      type="checkbox"
                      checked={settings.requireEmailVerification}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-klintblue focus:ring-klintblue border-gray-300 rounded"
                    />
                    <label htmlFor="requireEmailVerification" className="ml-2 block text-sm text-gray-900">
                      Require email verification for new accounts
                    </label>
                  </div>
                </div>
              </div>
            )}

            {/* File Upload Settings */}
            {activeTab === 'uploads' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">File Upload Settings</h3>
                
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Maximum File Size (MB)
                    </label>
                    <input
                      type="number"
                      name="maxFileSize"
                      value={settings.maxFileSize}
                      onChange={handleInputChange}
                      min="1"
                      max="100"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-klintblue"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Allowed File Types
                    </label>
                    <input
                      type="text"
                      name="allowedFileTypes"
                      value={settings.allowedFileTypes}
                      onChange={handleInputChange}
                      placeholder="jpg,jpeg,png,gif,webp"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-klintblue"
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      Comma-separated list of allowed file extensions
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Email Settings */}
            {activeTab === 'email' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">Email Settings</h3>
                
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Support Email
                    </label>
                    <input
                      type="email"
                      name="supportEmail"
                      value={settings.supportEmail}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-klintblue"
                    />
                  </div>
                </div>
                
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <span className="text-yellow-400">⚠️</span>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">
                        Email Configuration Required
                      </h3>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>
                          Email settings need to be configured in the backend environment variables.
                          Contact your system administrator to set up SMTP configuration.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Maintenance Settings */}
            {activeTab === 'maintenance' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">Maintenance Mode</h3>
                
                <div className="space-y-4">
                  <div className="flex items-center">
                    <input
                      id="maintenanceMode"
                      name="maintenanceMode"
                      type="checkbox"
                      checked={settings.maintenanceMode}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-klintblue focus:ring-klintblue border-gray-300 rounded"
                    />
                    <label htmlFor="maintenanceMode" className="ml-2 block text-sm text-gray-900">
                      Enable maintenance mode
                    </label>
                  </div>
                  
                  {settings.maintenanceMode && (
                    <div className="bg-red-50 border border-red-200 rounded-md p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <span className="text-red-400">🚨</span>
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-red-800">
                            Maintenance Mode Active
                          </h3>
                          <div className="mt-2 text-sm text-red-700">
                            <p>
                              When maintenance mode is enabled, only administrators can access the site.
                              Regular users will see a maintenance message.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Save Button */}
            <div className="pt-6 border-t border-gray-200">
              <div className="flex justify-end">
                <button
                  type="submit"
                  className="bg-klintblue text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-klintblue"
                >
                  Save Settings
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* System Information */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">System Information</h3>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <div>
            <dt className="text-sm font-medium text-gray-500">Platform Version</dt>
            <dd className="mt-1 text-sm text-gray-900">v1.0.0</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Database Status</dt>
            <dd className="mt-1 text-sm text-green-600">Connected</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Storage Provider</dt>
            <dd className="mt-1 text-sm text-gray-900">Cloudinary</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Last Backup</dt>
            <dd className="mt-1 text-sm text-gray-900">Not configured</dd>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AdminSettings;
