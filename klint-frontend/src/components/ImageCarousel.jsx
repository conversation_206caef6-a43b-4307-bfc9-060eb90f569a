import { useState } from "react";
import { isValidImageUrl } from "../utils/imageUtils";

/**
 * A simple image carousel component for product images
 * @param {Object} props Component props
 * @param {Array} props.images Array of image objects with url and is_primary properties
 * @param {string} props.productName Product name for alt text
 * @returns {JSX.Element} Carousel component
 */
const ImageCarousel = ({ images, productName }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // If no images, show placeholder
  if (!images || images.length === 0) {
    return (
      <div className="bg-gray-200 h-96 rounded-lg flex items-center justify-center">
        <span className="text-gray-500 text-lg">No Product Images</span>
      </div>
    );
  }

  // Navigate to previous image
  const prevImage = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? images.length - 1 : prevIndex - 1
    );
  };

  // Navigate to next image
  const nextImage = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === images.length - 1 ? 0 : prevIndex + 1
    );
  };

  // Get image URL with validation
  const getImageUrl = (image) => {
    const imageUrl = image.url;
    const isValid = isValidImageUrl(imageUrl);
    return isValid ? imageUrl : "/placeholder.svg";
  };

  return (
    <div className="relative">
      {/* Main image display */}
      <div className="bg-gray-200 h-96 mb-4 rounded-lg flex items-center justify-center overflow-hidden relative">
        <img
          src={getImageUrl(images[currentIndex])}
          alt={`${productName} - Image ${currentIndex + 1}`}
          className="h-full w-full object-contain"
          onError={(e) => {
            console.error("Carousel image failed to load:", e.target.src);
            e.target.onerror = null;
            e.target.src = "/placeholder.svg";
          }}
        />
        
        {/* Image counter */}
        {/* <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
          {currentIndex + 1} / {images.length}
        </div> */}
      </div>

      {/* Navigation buttons */}
      <button 
        onClick={prevImage}
        className="absolute top-1/2 left-2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
        aria-label="Previous image"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
      </button>
      
      <button 
        onClick={nextImage}
        className="absolute top-1/2 right-2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
        aria-label="Next image"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </button>

      {/* Thumbnail navigation */}
      <div className="grid grid-cols-5 gap-2">
        {images.map((image, index) => (
          <div
            key={index}
            className={`bg-gray-200 h-16 rounded-lg flex items-center justify-center overflow-hidden cursor-pointer ${
              index === currentIndex ? "ring-2 ring-klintblue" : ""
            }`}
            onClick={() => setCurrentIndex(index)}
          >
            <img
              src={getImageUrl(image)}
              alt={`${productName} - Thumbnail ${index + 1}`}
              className="h-full w-full object-cover"
              onError={(e) => {
                e.target.onerror = null;
                e.target.src = "/placeholder.svg";
              }}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default ImageCarousel;
