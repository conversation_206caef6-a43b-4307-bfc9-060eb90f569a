const express = require("express");
const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");
const { upload } = require("../utils/cloudinary");
const {
  createProduct,
  getProductById,
  getProductsByVendor,
  updateProduct,
  deleteProduct,
  getCategories,
  createCategory,
} = require("../queries");

// Middleware to check if user is a vendor
const isVendor = (req, res, next) => {
  if (req.user.role !== "vendor") {
    return res.status(403).json({
      success: false,
      message: "Access denied. Only vendors can manage products.",
    });
  }
  next();
};

// Get all categories
router.get("/categories", async (req, res) => {
  try {
    const categories = await getCategories();
    res.json({
      success: true,
      categories,
    });
  } catch (error) {
    console.error("Error fetching categories:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching categories",
      error: error.message,
    });
  }
});

// Create a new category (admin only)
router.post("/categories", authMiddleware(["admin"]), async (req, res) => {
  try {
    const { name, description, parent_id } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: "Category name is required",
      });
    }

    const category = await createCategory({ name, description, parent_id });

    res.status(201).json({
      success: true,
      message: "Category created successfully",
      category,
    });
  } catch (error) {
    console.error("Error creating category:", error);
    res.status(500).json({
      success: false,
      message: "Error creating category",
      error: error.message,
    });
  }
});

// Create a new product with image upload (vendor only)
router.post(
  "/",
  authMiddleware(["vendor"]),
  isVendor,
  upload.array("images", 5),
  async (req, res) => {
    try {
      const {
        name,
        description,
        price,
        stock_quantity,
        sku,
        category_id,
        attributes,
        is_active,
      } = req.body;

      // Convert is_active to boolean if it's a string
      let isActiveBoolean;
      if (is_active === undefined || is_active === null) {
        isActiveBoolean = true; // default to true
      } else if (typeof is_active === "string") {
        isActiveBoolean = is_active.toLowerCase() === "true";
      } else {
        isActiveBoolean = Boolean(is_active);
      }

      // Validate required fields
      if (!name || !price || !stock_quantity || !sku) {
        return res.status(400).json({
          success: false,
          message:
            "Missing required fields: name, price, stock_quantity, and sku are required",
        });
      }

      // Process uploaded images
      const images = req.files
        ? req.files.map((file) => ({
            url: file.path,
            is_primary: false,
          }))
        : [];

      // Set first image as primary if images exist
      if (images.length > 0) {
        images[0].is_primary = true;
      }

      // Create product
      const product = await createProduct(req.user.userId, {
        name,
        description,
        price,
        stock_quantity,
        sku,
        category_id,
        is_active: isActiveBoolean,
        attributes: attributes ? JSON.parse(attributes) : [],
        images,
      });

      res.status(201).json({
        success: true,
        message: "Product created successfully",
        product,
      });
    } catch (error) {
      console.error("Error creating product:", error);

      if (error.message === "SKU is already in use") {
        return res.status(409).json({
          success: false,
          message: "SKU is already in use",
        });
      }

      res.status(500).json({
        success: false,
        message: "Error creating product",
        error: error.message,
      });
    }
  }
);

// Get all products for the current vendor
router.get(
  "/vendor",
  authMiddleware(["vendor"]),
  isVendor,
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      const result = await getProductsByVendor(req.user.userId, page, limit);

      res.json({
        success: true,
        ...result,
      });
    } catch (error) {
      console.error("Error fetching vendor products:", error);
      res.status(500).json({
        success: false,
        message: "Error fetching products",
        error: error.message,
      });
    }
  }
);

// Get a single product by ID
router.get("/:id", async (req, res) => {
  try {
    const product = await getProductById(req.params.id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    res.json({
      success: true,
      product,
    });
  } catch (error) {
    console.error("Error fetching product:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching product",
      error: error.message,
    });
  }
});

// Update a product with image upload (vendor only)
router.put(
  "/:id",
  authMiddleware(["vendor"]),
  isVendor,
  upload.array("images", 5),
  async (req, res) => {
    try {
      const {
        name,
        description,
        price,
        stock_quantity,
        sku,
        category_id,
        attributes,
        existing_images,
        is_active,
      } = req.body;

      // Convert is_active to boolean if it's a string
      let isActiveBoolean;
      if (is_active === undefined || is_active === null) {
        isActiveBoolean = true; // default to true
      } else if (typeof is_active === "string") {
        isActiveBoolean = is_active.toLowerCase() === "true";
      } else {
        isActiveBoolean = Boolean(is_active);
      }

      // Validate required fields
      if (!name || !price || !stock_quantity || !sku) {
        return res.status(400).json({
          success: false,
          message:
            "Missing required fields: name, price, stock_quantity, and sku are required",
        });
      }

      // Process uploaded images
      const newImages = req.files
        ? req.files.map((file) => ({
            url: file.path,
            is_primary: false,
          }))
        : [];

      // Combine existing and new images
      let images = [];
      if (existing_images) {
        images = JSON.parse(existing_images);
      }
      images = [...images, ...newImages];

      // Ensure at least one primary image
      if (images.length > 0 && !images.some((img) => img.is_primary)) {
        images[0].is_primary = true;
      }

      // Update product
      const product = await updateProduct(req.params.id, req.user.userId, {
        name,
        description,
        price,
        stock_quantity,
        sku,
        category_id,
        is_active: isActiveBoolean,
        attributes: attributes ? JSON.parse(attributes) : [],
        images,
      });

      res.json({
        success: true,
        message: "Product updated successfully",
        product,
      });
    } catch (error) {
      console.error("Error updating product:", error);
      console.error("Error stack:", error.stack);
      console.error("Request body:", req.body);
      console.error("Request files:", req.files);

      if (
        error.message ===
        "Product not found or you don't have permission to update it"
      ) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message === "SKU is already in use") {
        return res.status(409).json({
          success: false,
          message: "SKU is already in use",
        });
      }

      res.status(500).json({
        success: false,
        message: "Error updating product",
        error: error.message,
        details: error.stack,
      });
    }
  }
);

// Delete a product (vendor only)
router.delete(
  "/:id",
  authMiddleware(["vendor"]),
  isVendor,
  async (req, res) => {
    try {
      await deleteProduct(req.params.id, req.user.userId);

      res.json({
        success: true,
        message: "Product deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting product:", error);

      if (
        error.message ===
        "Product not found or you don't have permission to delete it"
      ) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: "Error deleting product",
        error: error.message,
      });
    }
  }
);

module.exports = router;
