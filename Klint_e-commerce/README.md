# Klint E-commerce Platform

A robust e-commerce platform built with Node.js, Express, and MySQL, featuring multi-vendor support, product management, cart functionality, and secure authentication.

## Features

- **User Authentication & Authorization**
  - JWT-based authentication
  - Role-based access control (client, vendor, admin)
  - Secure password hashing
  - User profile management

- **Product Management**
  - Create, read, update, and delete products
  - Product categories and attributes
  - Multiple product images support
  - Stock quantity tracking
  - SKU management

- **Cart & Wishlist**
  - Add/remove items from cart
  - Update item quantities
  - Stock validation
  - Wishlist management

- **Vendor Features**
  - Vendor dashboard
  - Product listing management
  - Order management
  - Sales tracking

## Prerequisites

- Node.js (v14 or higher)
- MySQL (v8 or higher)
- npm

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user profile
- `PUT /api/auth/profile` - Update user profile


### Products
- `GET /api/products` - Get all products
- `GET /api/products/:id` - Get product by ID
- `POST /api/products` - Create new product (vendor only)
- `PUT /api/products/:id` - Update product (vendor only)
- `DELETE /api/products/:id` - Delete product (vendor only)
- `GET /api/products/category/:categoryId` - Get products by category
- `GET /api/products/vendor/:vendorId` - Get vendor's products

### Cart
- `GET /api/cart` - Get cart contents
- `POST /api/cart/add` - Add item to cart
- `DELETE /api/cart/remove/:productId` - Remove item from cart

### Wishlist
- `GET /api/wishlist` - Get wishlist contents
- `POST /api/wishlist/add` - Add item to wishlist
- `DELETE /api/wishlist/remove/:productId` - Remove item from wishlist

### Orders (Client)
- `POST /api/orders` - Create a new order
- `GET /api/orders` - Get all orders for the current user
- `GET /api/orders/:orderId` - Get specific order details

### Orders (Vendor)
- `GET /api/vendor/orders` - Get all orders containing vendor's products
- `GET /api/vendor/orders/:orderId` - Get details of a specific order
- `PATCH /api/vendor/orders/:orderId/status` - Update order status

### Categories
- `GET /api/categories` - Get all categories
- `POST /api/categories` - Create new category (admin only)
- `PUT /api/categories/:id` - Update category (admin only)
- `DELETE /api/categories/:id` - Delete category (admin only)

## Database Schema

The application uses the following main tables:
- `users` - User accounts and profiles
- `products` - Product information
- `categories` - Product categories
- `product_attributes` - Product specifications
- `product_images` - Product images
- `carts` - Shopping carts
- `cart_items` - Items in shopping carts
- `wishlists` - User wishlists
- `wishlist_items` - Items in wishlists
- `orders` - Order information
- `order_items` - Items in orders