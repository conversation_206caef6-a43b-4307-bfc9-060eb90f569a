import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import api from "../services/api";

function Home() {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch featured products and categories
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch products
        const productsResponse = await api.products.getAll({
          featured: true,
          limit: 4,
        });
        if (productsResponse.success && productsResponse.products) {
          // Ensure we only show a maximum of 4 products
          setFeaturedProducts(productsResponse.products.slice(0, 4));
        }

        // Fetch categories
        const categoriesResponse = await api.categories.getAll();
        if (categoriesResponse.success && categoriesResponse.categories) {
          setCategories(categoriesResponse.categories.slice(0, 4)); // Get first 4 categories
        }
      } catch (error) {
        console.error("Error fetching home page data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);
  return (
    <div className="container-custom py-8">
      <section className="mb-12">
        <div className="bg-klintblue text-white rounded-lg p-8 mb-8">
          <h1 className="text-4xl font-bold mb-4">Welcome to Klint</h1>
          <p className="text-xl mb-6">Discover amazing products</p>
          <Link
            to="/products"
            className="bg-white text-klintblue px-6 py-2 rounded-md font-medium hover:bg-gray-100 transition-colors inline-block"
          >
            Shop Now
          </Link>
        </div>
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Featured Products</h2>
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="w-16 h-16 border-4 border-klintblue border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-lg">Loading products...</p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredProducts.length > 0 ? (
              featuredProducts.map((product) => (
                <div
                  key={product.id}
                  className="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
                >
                  <Link to={`/products/${product.id}`} className="block">
                    <div className="h-48 bg-gray-200 flex items-center justify-center overflow-hidden">
                      {product.images && product.images.length > 0 ? (
                        <img
                          src={
                            product.images.find((img) => img.is_primary)?.url ||
                            product.images[0].url
                          }
                          alt={product.name}
                          className="h-full w-full object-cover"
                          onError={(e) => {
                            console.log(
                              "Featured product image failed to load:",
                              e.target.src
                            );
                            e.target.onerror = null;
                            e.target.src = "/placeholder.svg";
                          }}
                        />
                      ) : (
                        <span className="text-gray-500">No Image</span>
                      )}
                    </div>
                    <div className="p-4">
                      <h3 className="font-medium text-lg hover:text-klintblue transition-colors">
                        {product.name}
                      </h3>
                      <p className="text-gray-600 text-sm mb-2">
                        {product.category_name || "Uncategorized"}
                      </p>
                      <p className="font-bold text-klintblue">
                        ${parseFloat(product.price).toFixed(2)}
                      </p>
                    </div>
                  </Link>
                  <div className="px-4 pb-4">
                    <Link
                      to={`/products/${product.id}`}
                      className="btn-primary w-full block text-center"
                    >
                      View Details
                    </Link>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-span-4 text-center py-8">
                <p className="text-gray-500">No featured products available</p>
              </div>
            )}
          </div>
        )}

        {!loading && featuredProducts.length > 0 && (
          <div className="mt-8 text-center">
            {/* <Link to="/products" className="btn-secondary inline-block px-8">
              View All Products
            </Link> */}
          </div>
        )}
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Categories</h2>
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <div className="w-12 h-12 border-4 border-klintblue border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
              <p>Loading categories...</p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {categories.length > 0 ? (
              categories.map((category) => (
                <Link
                  key={category.id}
                  to={`/products?category=${category.id}`}
                  className="block"
                >
                  <div className="bg-gray-100 p-6 rounded-lg text-center hover:bg-gray-200 transition-colors">
                    <h3 className="font-medium">{category.name}</h3>
                  </div>
                </Link>
              ))
            ) : (
              <div className="col-span-4 text-center py-4">
                <p className="text-gray-500">No categories available</p>
              </div>
            )}
          </div>
        )}

        {!loading && categories.length > 0 && (
          <div className="mt-6 text-center">
            {/* <Link to="/categories" className="btn-secondary inline-block px-8">
              View All Categories
            </Link> */}
          </div>
        )}
      </section>
    </div>
  );
}

export default Home;
