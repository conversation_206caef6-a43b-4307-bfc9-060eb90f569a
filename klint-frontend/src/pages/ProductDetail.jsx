import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import api from "../services/api";

import ImageCarousel from "../components/ImageCarousel";

function ProductDetail() {
  const { id } = useParams();
  const [quantity, setQuantity] = useState(1);
  const [loading, setLoading] = useState(true);
  const [product, setProduct] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Fetch product details from API
    const fetchProduct = async () => {
      setLoading(true);
      try {
        const response = await api.products.getById(id);

        if (response.success && response.product) {
          // Format the product data for display
          const productData = {
            ...response.product,
            // Ensure we have default values for missing properties
            rating: response.product.rating || 4.0,
            reviews: response.product.reviews || 0,
            stock: response.product.stock_quantity || 0,
            // Ensure price is a number
            price: parseFloat(response.product.price) || 0,
          };

          // Log product data for debugging
          console.log("Product data:", productData);

          // Log attributes specifically
          if (productData.attributes && productData.attributes.length > 0) {
            console.log("Product attributes:", productData.attributes);
          } else {
            console.log("No product attributes found");
          }

          // Log image data specifically with detailed information
          if (productData.images && productData.images.length > 0) {
            console.log("Product images:", productData.images);
            // Log each image URL for debugging
            productData.images.forEach((img, index) => {
              console.log(`Image ${index + 1}:`, {
                url: img.url,
                is_primary: img.is_primary,
                complete_url: img.url,
                type: typeof img.url,
              });
            });
          } else {
            console.log("No product images found");
          }

          // Log category information
          console.log("Category information:", {
            category_id: productData.category_id,
            category_name: productData.category_name,
          });

          // If we have a category_id but no category_name, try to fetch the category name
          if (productData.category_id && !productData.category_name) {
            try {
              // Fetch all categories
              const categoriesResponse = await api.categories.getAll();
              if (categoriesResponse.success && categoriesResponse.categories) {
                // Find the matching category
                const category = categoriesResponse.categories.find(
                  (cat) =>
                    parseInt(cat.id) === parseInt(productData.category_id)
                );

                if (category) {
                  console.log("Found category name:", category.name);
                  productData.category_name = category.name;
                }
              }
            } catch (categoryErr) {
              console.error("Error fetching category name:", categoryErr);
              // Don't fail the whole product fetch if category name fetch fails
            }
          }

          setProduct(productData);
          setError(null);

          // Set initial quantity to 1 or max stock if less than 1
          setQuantity(productData.stock > 0 ? 1 : 0);
        } else {
          throw new Error("Invalid response format");
        }
      } catch (err) {
        setError("Failed to load product. Please try again later.");
        console.error("Error fetching product:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [id]);

  const handleQuantityChange = (e) => {
    const value = parseInt(e.target.value);
    if (value > 0 && value <= product.stock) {
      setQuantity(value);
    }
  };

  const handleAddToCart = async () => {
    try {
      const response = await api.cart.addItem(product.id, quantity);
      if (response.success) {
        alert(`Added ${quantity} of ${product.name} to cart!`);
      } else {
        throw new Error(response.message || "Failed to add to cart");
      }
    } catch (err) {
      console.error("Error adding to cart:", err);
      alert("Failed to add product to cart. Please try again.");
    }
  };

  const handleAddToWishlist = async () => {
    try {
      const response = await api.wishlist.addItem(product.id);
      if (response.success) {
        alert(`Added ${product.name} to your wishlist!`);
      } else {
        throw new Error(response.message || "Failed to add to wishlist");
      }
    } catch (err) {
      console.error("Error adding to wishlist:", err);
      alert("Failed to add product to wishlist. Please try again.");
    }
  };

  // Handle loading state
  if (loading) {
    return (
      <div className="container-custom py-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-klintblue border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-lg">Loading product details...</p>
          </div>
        </div>
      </div>
    );
  }

  // Handle error state
  if (error || !product) {
    return (
      <div className="container-custom py-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error || "Product not found"}</p>
        </div>
        <Link to="/products" className="btn-primary">
          Back to Products
        </Link>
      </div>
    );
  }

  return (
    <div className="container-custom py-8">
      {/* Back button */}
      <div className="mb-6">
        <Link
          to="/products"
          className="flex items-center text-klintblue hover:underline"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-1"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
              clipRule="evenodd"
            />
          </svg>
          Back to Products
        </Link>
      </div>

      <div className="flex flex-col md:flex-row gap-8">
        {/* Product Images */}
        <div className="md:w-1/2">
          {/* Image Carousel */}
          <ImageCarousel
            images={product.images || []}
            productName={product.name}
          />
        </div>

        {/* Product Info */}
        <div className="md:w-1/2">
          <h1 className="text-3xl font-bold mb-2">{product.name}</h1>
          <div className="flex items-center mb-4">
            <div className="flex text-yellow-400 mr-2">
              {[...Array(5)].map((_, i) => (
                <span key={i}>★</span>
              ))}
            </div>
            <span className="text-gray-600">{product.reviews} reviews</span>
          </div>

          <p className="text-2xl font-bold text-klintblue mb-4">
            ${parseFloat(product.price).toFixed(2)}
          </p>

          <div className="mb-6">
            <h2 className="font-medium mb-2">Description</h2>
            <p className="text-gray-700">{product.description}</p>
          </div>

          <div className="mb-6">
            <h2 className="font-medium mb-2">Category</h2>
            <p className="text-gray-700">
              {product.category_name || "Uncategorized"}
              {product.category_id && !product.category_name && (
                <span className="text-xs text-gray-500 ml-2">
                  (ID: {product.category_id})
                </span>
              )}
            </p>
            {/* Add link to category products */}
            {product.category_id && (
              <Link
                to={`/products?category=${product.category_id}`}
                className="text-sm text-klintblue hover:underline mt-1 inline-block"
              >
                View all products in this category
              </Link>
            )}
          </div>

          {/* Product Attributes */}
          {/* {product.attributes && product.attributes.length > 0 && (
            <div className="mb-6">
              <h2 className="font-medium mb-2">Specifications</h2>
              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-3">
                  {product.attributes.map((attr, index) => (
                    <div
                      key={index}
                      className="flex justify-between border-b border-gray-200 py-2 last:border-b-0"
                    >
                      <span className="text-gray-600 font-medium">
                        {attr.name}
                      </span>
                      <span className="text-gray-800 font-semibold">
                        {attr.value}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )} */}

          <div className="mb-6">
            <h2 className="font-medium mb-2">Availability</h2>
            <p
              className={product.stock > 0 ? "text-green-600" : "text-red-600"}
            >
              {product.stock > 0
                ? `In Stock (${product.stock} available)`
                : "Out of Stock"}
            </p>
          </div>

          <div className="flex items-center mb-6">
            <label htmlFor="quantity" className="mr-4 font-medium">
              Quantity:
            </label>
            <input
              type="number"
              id="quantity"
              min="1"
              max={product.stock}
              value={quantity}
              onChange={handleQuantityChange}
              className="border rounded-md p-2 w-20"
            />
          </div>

          <div className="flex gap-4">
            <button
              className="btn-primary flex-1"
              onClick={handleAddToCart}
              disabled={product.stock <= 0}
            >
              Add to Cart
            </button>
            <button
              className="btn-secondary flex-1"
              onClick={handleAddToWishlist}
            >
              Add to Wishlist
            </button>
          </div>
        </div>
      </div>

      {/* Technical Specifications Section */}
      {((product.attributes && product.attributes.length > 0) ||
        product.category_name) && (
        <div className="mt-12 mb-12">
          <h2 className="text-2xl font-bold mb-6">Specifications</h2>
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
              {/* Always show category if available */}
              {product.category_name && (
                <div className="border-b border-gray-200 py-3">
                  <h3 className="text-gray-600 text-sm mb-1">Category</h3>
                  <p className="font-medium">{product.category_name}</p>
                </div>
              )}

              {/* Show all attributes */}
              {product.attributes &&
                product.attributes.map((attr, index) => (
                  <div
                    key={index}
                    className="border-b border-gray-200 py-3 last:border-b-0"
                  >
                    <h3 className="text-gray-600 text-sm mb-1">{attr.name}</h3>
                    <p className="font-medium">{attr.value}</p>
                  </div>
                ))}
            </div>
          </div>
        </div>
      )}

      {/* Reviews Section */}
      <div className="mt-12">
        <h2 className="text-2xl font-bold mb-6">Customer Reviews</h2>

        <div className="mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm mb-4">
            <div className="flex justify-between mb-2">
              <h3 className="font-medium">John Doe</h3>
              <div className="flex text-yellow-400">
                {[...Array(5)].map((_, i) => (
                  <span key={i}>★</span>
                ))}
              </div>
            </div>
            <p className="text-gray-600 text-sm mb-2">Posted on May 10, 2023</p>
            <p>Great product! Exactly as described and arrived quickly.</p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex justify-between mb-2">
              <h3 className="font-medium">Jane Smith</h3>
              <div className="flex text-yellow-400">
                {[...Array(4)].map((_, i) => (
                  <span key={i}>★</span>
                ))}
              </div>
            </div>
            <p className="text-gray-600 text-sm mb-2">
              Posted on April 28, 2023
            </p>
            <p>Good quality for the price. Would recommend.</p>
          </div>
        </div>

        <button className="btn-secondary">Load More Reviews</button>
      </div>
    </div>
  );
}

export default ProductDetail;
