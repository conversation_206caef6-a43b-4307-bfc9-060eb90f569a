import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import api from "../../services/api";
import VendorNav from "../../components/vendor/VendorNav";

// Helper function to parse product images
const parseProductImages = (product) => {
  if (!product.images) return [];

  // If images is already an array of objects with url property, return it
  if (
    Array.isArray(product.images) &&
    product.images.length > 0 &&
    typeof product.images[0] === "object" &&
    product.images[0].url
  ) {
    return product.images;
  }

  // If images is a string (from GROUP_CONCAT in MySQL), parse it
  if (typeof product.images === "string") {
    try {
      // Format from backend is typically "url||is_primary,url||is_primary"
      return product.images.split(",").map((img) => {
        const [url, isPrimary] = img.split("||");
        return {
          url: url,
          is_primary: isPrimary === "true" || isPrimary === "1",
        };
      });
    } catch (e) {
      console.error("Error parsing product images:", e);
      return [];
    }
  }

  // If images is an array of strings, convert to objects
  if (Array.isArray(product.images)) {
    return product.images.map((img, index) => {
      if (typeof img === "string") {
        return { url: img, is_primary: index === 0 };
      }
      return img;
    });
  }

  return [];
};

function VendorProducts() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [sortBy, setSortBy] = useState("newest");
  const [confirmDelete, setConfirmDelete] = useState(null);

  useEffect(() => {
    fetchProducts();
  }, []);

  // We don't need this effect anymore since we're processing images in fetchProducts
  // useEffect(() => {
  //   if (products.length > 0) {
  //     const processedProducts = products.map((product) => {
  //       const parsedImages = parseProductImages(product);
  //       return {
  //         ...product,
  //         parsedImages,
  //       };
  //     });
  //     setProducts(processedProducts);
  //   }
  // }, []);

  const fetchProducts = async () => {
    setLoading(true);
    try {
      // Get all categories first to have category names available
      const categoriesResponse = await api.categories.getAll();
      const categories = categoriesResponse.success
        ? categoriesResponse.categories
        : [];

      // Get vendor products
      const response = await api.vendor.getProducts();

      if (response.success) {
        // Add category names to products
        const productsWithCategories = (response.products || []).map(
          (product) => {
            // Find category name if category_id exists
            let categoryName = "Uncategorized";
            if (product.category_id && categories.length > 0) {
              const category = categories.find(
                (cat) => cat.id === parseInt(product.category_id)
              );
              if (category) {
                categoryName = category.name;
              }
            }

            // Parse images
            const parsedImages = parseProductImages(product);

            return {
              ...product,
              category_name: categoryName,
              parsedImages,
            };
          }
        );

        setProducts(productsWithCategories);
      } else {
        setError("Failed to fetch products");
      }
    } catch (error) {
      console.error("Error fetching products:", error);
      setError("An error occurred while fetching products");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteProduct = async (productId) => {
    try {
      const response = await api.vendor.deleteProduct(productId);

      if (response.success) {
        // Remove the deleted product from the state
        setProducts(products.filter((product) => product.id !== productId));
        setConfirmDelete(null);
      } else {
        setError("Failed to delete product");
      }
    } catch (error) {
      console.error("Error deleting product:", error);
      setError("An error occurred while deleting the product");
    }
  };

  const handleToggleProductStatus = async (productId, currentStatus) => {
    try {
      // Use the new API method to update the product status
      const response = await api.vendor.updateProductStatus(
        productId,
        !currentStatus
      );

      if (response.success) {
        // Update the product status in the state
        setProducts(
          products.map((p) =>
            p.id === productId ? { ...p, is_active: !currentStatus } : p
          )
        );
      } else {
        setError(response.message || "Failed to update product status");
      }
    } catch (error) {
      console.error("Error updating product status:", error);
      setError("An error occurred while updating product status");
    }
  };

  // Filter and sort products
  const filteredProducts = products
    .filter((product) => {
      // Filter by search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          product.name.toLowerCase().includes(query) ||
          product.sku.toLowerCase().includes(query) ||
          (product.description &&
            product.description.toLowerCase().includes(query))
        );
      }
      return true;
    })
    .filter((product) => {
      // Filter by status
      if (filterStatus === "active") return product.is_active;
      if (filterStatus === "inactive") return !product.is_active;
      return true; // 'all'
    })
    .sort((a, b) => {
      // Sort products
      switch (sortBy) {
        case "name-asc":
          return a.name.localeCompare(b.name);
        case "name-desc":
          return b.name.localeCompare(a.name);
        case "price-asc":
          return parseFloat(a.price) - parseFloat(b.price);
        case "price-desc":
          return parseFloat(b.price) - parseFloat(a.price);
        case "stock-asc":
          return a.stock_quantity - b.stock_quantity;
        case "stock-desc":
          return b.stock_quantity - a.stock_quantity;
        case "oldest":
          return new Date(a.created_at) - new Date(b.created_at);
        case "newest":
        default:
          return new Date(b.created_at) - new Date(a.created_at);
      }
    });

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="w-16 h-16 border-4 border-klintblue border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="container-custom py-4">
      <VendorNav />

      <div className="flex justify-between items-center mb-2">
        <h1 className="text-base font-bold">My Products</h1>
        <Link
          to="/vendor/products/new"
          className="bg-klintblue text-white px-2 py-1 text-sm rounded hover:bg-klintAccent transition-colors"
        >
          Add New Product
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-2 py-1.5 rounded text-sm mb-2">
          <p>{error}</p>
        </div>
      )}

      {/* Filters and Search - Compact */}
      <div className="bg-white rounded shadow-sm p-2 mb-2">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
          <div className="flex-1">
            <div className="relative">
              <input
                type="text"
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-6 pr-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-klintblue"
              />
              <div className="absolute left-2 top-1.5 text-gray-400">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-3 w-3"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-klintblue"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-klintblue"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="name-asc">Name (A-Z)</option>
              <option value="name-desc">Name (Z-A)</option>
              <option value="price-asc">Price (Low to High)</option>
              <option value="price-desc">Price (High to Low)</option>
              <option value="stock-asc">Stock (Low to High)</option>
              <option value="stock-desc">Stock (High to Low)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Products List - Compact */}
      <div className="bg-white rounded shadow-sm overflow-hidden">
        {filteredProducts.length === 0 ? (
          <div className="p-4 text-center">
            <p className="text-gray-500 text-sm mb-2">No products found.</p>
            <Link
              to="/vendor/products/new"
              className="bg-klintblue text-white px-3 py-1 text-sm rounded hover:bg-klintAccent transition-colors"
            >
              Add Your First Product
            </Link>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-sm font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Product
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    SKU
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Price
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Stock
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Status
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredProducts.map((product) => (
                  <tr key={product.id} className="hover:bg-gray-50">
                    <td className="px-3 py-2 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-8 w-8 flex-shrink-0 bg-gray-200 rounded overflow-hidden">
                          {product.parsedImages &&
                          product.parsedImages.length > 0 ? (
                            <img
                              src={product.parsedImages[0].url}
                              alt={product.name}
                              className="h-full w-full object-cover"
                              onError={(e) => {
                                e.target.onerror = null;
                                e.target.src =
                                  "https://via.placeholder.com/150?text=No+Image";
                              }}
                            />
                          ) : (
                            <div className="h-full w-full flex items-center justify-center text-gray-500 text-xs">
                              No img
                            </div>
                          )}
                        </div>
                        <div className="ml-2">
                          <div className="text-sm font-medium text-gray-900">
                            {product.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {product.category_name || "Uncategorized"}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {product.sku}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                      ${parseFloat(product.price).toFixed(2)}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {product.stock_quantity}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap">
                      <span
                        className={`px-1.5 py-0.5 inline-flex text-sm leading-4 font-medium rounded-full ${
                          product.is_active
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {product.is_active ? "Active" : "Inactive"}
                      </span>
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <Link
                          to={`/vendor/products/edit/${product.id}`}
                          className="text-indigo-600 hover:text-indigo-900"
                        >
                          Edit
                        </Link>
                        <button
                          onClick={() => setConfirmDelete(product.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal - Compact */}
      {confirmDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded p-3 max-w-xs w-full">
            <h3 className="text-base font-semibold mb-2">Confirm Delete</h3>
            <p className="text-sm mb-3">
              Are you sure you want to delete this product? This action cannot
              be undone.
            </p>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => setConfirmDelete(null)}
                className="px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
              >
                Cancel
              </button>
              <button
                onClick={() => handleDeleteProduct(confirmDelete)}
                className="px-2 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default VendorProducts;
