import React, { createContext, useState, useContext, useEffect } from "react";
import api from "../services/api";
import { useAuth } from "./AuthContext";

// Create the context
const CartContext = createContext();

// Custom hook to use the cart context
export const useCart = () => {
  return useContext(CartContext);
};

// Provider component
export const CartProvider = ({ children }) => {
  const [cartItems, setCartItems] = useState([]);
  const [cartId, setCartId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { isAuthenticated } = useAuth();

  // Fetch cart data when user authentication status changes
  useEffect(() => {
    if (isAuthenticated) {
      fetchCart();
    } else {
      // Clear cart when user logs out
      setCartItems([]);
      setCartId(null);
    }
  }, [isAuthenticated]);

  // Fetch cart data from API
  const fetchCart = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.cart.get();

      if (response && response.success) {
        setCartItems(response.cart.items || []);
        setCartId(response.cart.cart_id);
      } else {
        throw new Error("Failed to fetch cart data");
      }
    } catch (err) {
      console.error("Error fetching cart:", err);
      setError("Failed to load cart. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Add item to cart
  const addItem = async (productId, quantity = 1) => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.cart.addItem(productId, quantity);

      if (response && response.success) {
        // Refresh cart after adding item
        await fetchCart();
        return { success: true };
      } else {
        throw new Error(response?.message || "Failed to add item to cart");
      }
    } catch (err) {
      console.error("Error adding item to cart:", err);
      setError("Failed to add item to cart. Please try again.");
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  };

  // Remove item from cart with optimistic update
  const removeItem = async (productId) => {
    try {
      setError(null);

      // Save current cart state for rollback if needed
      const previousCartItems = [...cartItems];

      // Optimistically update the UI by removing the item
      setCartItems(cartItems.filter((item) => item.product_id !== productId));

      // Make the API call in the background
      const response = await api.cart.removeItem(productId);

      if (!response || !response.success) {
        throw new Error(response?.message || "Failed to remove item from cart");
      }

      return { success: true };
    } catch (err) {
      console.error("Error removing item from cart:", err);

      // Revert to previous state on error
      setCartItems(previousCartItems);
      setError("Failed to remove item from cart. Please try again.");
      return { success: false, error: err.message };
    }
  };

  // Update item quantity with optimistic update
  const updateItemQuantity = async (productId, quantity) => {
    try {
      setError(null);

      // Save current cart state for rollback if needed
      const previousCartItems = [...cartItems];

      // Find the item to update
      const itemIndex = cartItems.findIndex(
        (item) => item.product_id === productId
      );

      if (itemIndex === -1) {
        throw new Error("Item not found in cart");
      }

      // Create a new cart items array with the updated quantity
      const updatedCartItems = [...cartItems];
      updatedCartItems[itemIndex] = {
        ...updatedCartItems[itemIndex],
        quantity: quantity,
      };

      // Optimistically update the UI
      setCartItems(updatedCartItems);

      // Make the API calls in the background
      // First remove the item
      await api.cart.removeItem(productId);

      // Then add it back with the new quantity
      const response = await api.cart.addItem(productId, quantity);

      if (!response || !response.success) {
        throw new Error(response?.message || "Failed to update item quantity");
      }

      return { success: true };
    } catch (err) {
      console.error("Error updating item quantity:", err);

      // Revert to previous state on error
      setCartItems(previousCartItems);
      setError("Failed to update item quantity. Please try again.");
      return { success: false, error: err.message };
    }
  };

  // Clear cart with optimistic update
  const clearCart = async () => {
    try {
      setError(null);

      // Save current cart state for rollback if needed
      const previousCartItems = [...cartItems];

      // Optimistically update the UI by clearing the cart
      setCartItems([]);

      // Make the API calls in the background
      let success = true;

      // For each item in the previous cart, remove it
      for (const item of previousCartItems) {
        try {
          await api.cart.removeItem(item.product_id);
        } catch (err) {
          console.error(`Error removing item ${item.product_id}:`, err);
          success = false;
        }
      }

      if (!success) {
        throw new Error("Failed to remove some items from cart");
      }

      return { success: true };
    } catch (err) {
      console.error("Error clearing cart:", err);

      // Revert to previous state on error
      setCartItems(previousCartItems);
      setError("Failed to clear cart. Please try again.");
      return { success: false, error: err.message };
    }
  };

  // Calculate cart totals
  const calculateTotals = () => {
    const subtotal = cartItems.reduce(
      (total, item) => total + parseFloat(item.price) * item.quantity,
      0
    );
    const shipping = subtotal > 0 ? 10 : 0; // $10 shipping fee if cart is not empty
    const tax = subtotal * 0.1; // 10% tax
    const total = subtotal + shipping + tax;

    return {
      subtotal,
      shipping,
      tax,
      total,
    };
  };

  // Get cart item count
  const getCartItemCount = () => {
    return cartItems.reduce((count, item) => count + item.quantity, 0);
  };

  // Context value
  const value = {
    cartItems,
    cartId,
    loading,
    error,
    fetchCart,
    addItem,
    removeItem,
    updateItemQuantity,
    clearCart,
    calculateTotals,
    getCartItemCount,
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
};

export default CartContext;
