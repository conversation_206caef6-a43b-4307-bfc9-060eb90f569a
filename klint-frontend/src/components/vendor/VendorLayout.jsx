import React, { useState } from 'react';
import { Link, Outlet, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

function VendorLayout() {
  const { currentUser, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleLogout = async () => {
    await logout();
    navigate('/signin');
  };

  const isActive = (path) => {
    return location.pathname === path || location.pathname.startsWith(`${path}/`);
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-100">
      {/* Vendor Dashboard Header */}
      <header className="bg-white shadow-sm">
        <div className="container-custom py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link to="/" className="text-2xl font-bold text-klintblue mr-8">
                Klint
              </Link>
              <h1 className="text-xl font-semibold text-gray-800">Vendor Dashboard</h1>
            </div>

            <div className="flex items-center space-x-4">
              <Link to="/" className="text-gray-600 hover:text-klintblue">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                </svg>
              </Link>

              <div className="relative">
                <button
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                  className="flex items-center text-gray-700 hover:text-klintblue md:hidden"
                >
                  <span className="mr-2">{currentUser?.first_name || 'Account'}</span>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>

                {isMobileMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 md:hidden">
                    <Link
                      to="/account"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      My Account
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Sign Out
                    </button>
                  </div>
                )}

                <div className="hidden md:flex items-center">
                  <span className="mr-2 text-gray-700">{currentUser?.first_name || 'Account'}</span>
                  <button
                    onClick={handleLogout}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    Sign Out
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex flex-1 container-custom py-6">
        {/* Sidebar Navigation */}
        <aside className="w-64 bg-white shadow-sm rounded-lg p-4 mr-6 hidden md:block">
          <nav className="space-y-1">
            <Link
              to="/vendor/dashboard"
              className={`block px-4 py-2 rounded-md ${
                isActive('/vendor/dashboard')
                  ? 'bg-klintblue text-white'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z" />
                  <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z" />
                </svg>
                Dashboard
              </div>
            </Link>
            
            <Link
              to="/vendor/products"
              className={`block px-4 py-2 rounded-md ${
                isActive('/vendor/products')
                  ? 'bg-klintblue text-white'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z" clipRule="evenodd" />
                </svg>
                Products
              </div>
            </Link>
            
            <Link
              to="/vendor/orders"
              className={`block px-4 py-2 rounded-md ${
                isActive('/vendor/orders')
                  ? 'bg-klintblue text-white'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                  <path fillRule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
                </svg>
                Orders
              </div>
            </Link>
            
            <Link
              to="/vendor/analytics"
              className={`block px-4 py-2 rounded-md ${
                isActive('/vendor/analytics')
                  ? 'bg-klintblue text-white'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                </svg>
                Analytics
              </div>
            </Link>
            
            <Link
              to="/account"
              className={`block px-4 py-2 rounded-md ${
                isActive('/account')
                  ? 'bg-klintblue text-white'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
                </svg>
                My Account
              </div>
            </Link>
          </nav>
        </aside>

        {/* Mobile Navigation */}
        <div className="md:hidden w-full mb-6">
          <div className="bg-white shadow-sm rounded-lg p-4 flex overflow-x-auto space-x-4">
            <Link
              to="/vendor/dashboard"
              className={`flex-shrink-0 px-4 py-2 rounded-md ${
                isActive('/vendor/dashboard')
                  ? 'bg-klintblue text-white'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              Dashboard
            </Link>
            
            <Link
              to="/vendor/products"
              className={`flex-shrink-0 px-4 py-2 rounded-md ${
                isActive('/vendor/products')
                  ? 'bg-klintblue text-white'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              Products
            </Link>
            
            <Link
              to="/vendor/orders"
              className={`flex-shrink-0 px-4 py-2 rounded-md ${
                isActive('/vendor/orders')
                  ? 'bg-klintblue text-white'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              Orders
            </Link>
            
            <Link
              to="/vendor/analytics"
              className={`flex-shrink-0 px-4 py-2 rounded-md ${
                isActive('/vendor/analytics')
                  ? 'bg-klintblue text-white'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              Analytics
            </Link>
          </div>
        </div>

        {/* Main Content */}
        <main className="flex-1">
          <Outlet />
        </main>
      </div>
    </div>
  );
}

export default VendorLayout;
