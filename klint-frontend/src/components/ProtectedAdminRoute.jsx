import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

function ProtectedAdminRoute() {
  const { isAuthenticated, currentUser, loading } = useAuth();

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="container-custom py-12 flex justify-center">
        <div className="w-16 h-16 border-4 border-klintblue border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  // If not authenticated, redirect to sign in
  if (!isAuthenticated) {
    return <Navigate to="/signin" replace />;
  }

  // If authenticated but not an admin, redirect to access denied page
  if (currentUser.role !== 'admin') {
    return (
      <div className="container-custom py-12 text-center">
        <h1 className="text-3xl font-bold mb-4">Access Denied</h1>
        <p className="mb-6">You don't have permission to access the admin dashboard.</p>
        <p className="text-gray-600">
          This area is restricted to admin accounts only. If you're an admin, please sign in with your admin account.
        </p>
      </div>
    );
  }

  // If authenticated and is an admin, render the protected content
  return <Outlet />;
}

export default ProtectedAdminRoute;
