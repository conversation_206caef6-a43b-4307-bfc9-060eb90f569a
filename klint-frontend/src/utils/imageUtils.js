/**
 * Utility functions for handling images
 */

/**
 * Checks if a URL is a valid image URL
 * @param {string} url - The URL to check
 * @returns {boolean} - Whether the URL is valid
 */
export const isValidImageUrl = (url) => {
  if (!url) return false;

  // Check if it's a string
  if (typeof url !== "string") return false;

  // Check if it's empty or just whitespace
  if (url.trim() === "") return false;

  // Check if it's a valid URL format
  try {
    // If the URL doesn't start with http:// or https://, assume it's a relative URL
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
      // For relative URLs, just check if it's not empty
      return url.trim() !== "";
    }

    // For absolute URLs, try to parse it
    new URL(url);
  } catch (e) {
    console.error("Invalid URL format:", url);
    return false;
  }

  // Check if it has a valid image extension or is a data URL
  if (url.startsWith("data:image/")) {
    return true; // Data URLs for images are valid
  }

  // For regular URLs, check extensions
  const validExtensions = [".jpg", ".jpeg", ".png", ".gif", ".svg", ".webp"];
  const hasValidExtension = validExtensions.some((ext) =>
    url.toLowerCase().endsWith(ext)
  );

  // If it doesn't have a valid extension, it might still be a valid image URL
  // (e.g., from a CDN or with query parameters)
  if (!hasValidExtension) {
    console.warn("URL does not have a standard image extension:", url);
    // We'll still return true and let the image element's error handler deal with it
  }

  return true;
};

/**
 * Gets a placeholder image URL if the provided URL is invalid
 * @param {string} url - The original image URL
 * @returns {string} - Either the original URL if valid, or a placeholder
 */
export const getImageUrl = (url) => {
  return isValidImageUrl(url) ? url : "/placeholder.svg";
};
