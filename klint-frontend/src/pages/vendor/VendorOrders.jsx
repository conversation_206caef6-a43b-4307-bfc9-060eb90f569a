import React, { useState, useEffect } from "react";
import { Link, useSearchParams } from "react-router-dom";
import api from "../../services/api";
import VendorNav from "../../components/vendor/VendorNav";

function VendorOrders() {
  const [searchParams, setSearchParams] = useSearchParams();
  const initialStatus = searchParams.get("status") || "all";

  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [statusFilter, setStatusFilter] = useState(initialStatus);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState("newest");

  useEffect(() => {
    fetchOrders();
  }, [statusFilter]);

  const fetchOrders = async () => {
    setLoading(true);
    try {
      const response = await api.vendor.getOrders(
        statusFilter !== "all" ? statusFilter : null
      );

      if (response.success) {
        setOrders(response.orders || []);
      } else {
        setError("Failed to fetch orders");
      }
    } catch (error) {
      console.error("Error fetching orders:", error);
      setError("An error occurred while fetching orders");
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = (status) => {
    setStatusFilter(status);
    setSearchParams(status === "all" ? {} : { status });
  };

  const handleUpdateOrderStatus = async (orderId, newStatus) => {
    try {
      const response = await api.vendor.updateOrderStatus(orderId, newStatus);

      if (response.success) {
        // Update the order status in the state
        setOrders(
          orders.map((order) =>
            order.id === orderId ? { ...order, status: newStatus } : order
          )
        );
      } else {
        setError("Failed to update order status");
      }
    } catch (error) {
      console.error("Error updating order status:", error);
      setError("An error occurred while updating order status");
    }
  };

  // Filter and sort orders
  const filteredOrders = orders
    .filter((order) => {
      // Filter by search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          order.id.toString().includes(query) ||
          (order.customer_name &&
            order.customer_name.toLowerCase().includes(query))
        );
      }
      return true;
    })
    .sort((a, b) => {
      // Sort orders
      switch (sortBy) {
        case "oldest":
          return new Date(a.created_at) - new Date(b.created_at);
        case "total-asc":
          return parseFloat(a.total_amount) - parseFloat(b.total_amount);
        case "total-desc":
          return parseFloat(b.total_amount) - parseFloat(a.total_amount);
        case "newest":
        default:
          return new Date(b.created_at) - new Date(a.created_at);
      }
    });

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "processing":
        return "bg-blue-100 text-blue-800";
      case "shipped":
        return "bg-purple-100 text-purple-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-yellow-100 text-yellow-800";
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="w-16 h-16 border-4 border-klintblue border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="container-custom py-4">
      <VendorNav />

      <h1 className="text-base font-bold mb-2">Orders Management</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-2 py-1.5 rounded text-xs mb-2">
          <p>{error}</p>
        </div>
      )}

      {/* Filters and Search - Compact */}
      <div className="bg-white rounded shadow-sm p-2 mb-2">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search by order ID or customer..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-klintblue"
            />
          </div>

          <div className="flex flex-wrap gap-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-klintblue"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="total-asc">Total (Low to High)</option>
              <option value="total-desc">Total (High to Low)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Status Tabs - Compact */}
      <div className="bg-white rounded shadow-sm p-2 mb-2">
        <div className="flex flex-wrap gap-1">
          <button
            onClick={() => handleStatusChange("all")}
            className={`px-2 py-1 rounded text-xs ${
              statusFilter === "all"
                ? "bg-klintblue text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            All
          </button>
          <button
            onClick={() => handleStatusChange("pending")}
            className={`px-2 py-1 rounded text-xs ${
              statusFilter === "pending"
                ? "bg-klintblue text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            Pending
          </button>
          <button
            onClick={() => handleStatusChange("processing")}
            className={`px-2 py-1 rounded text-xs ${
              statusFilter === "processing"
                ? "bg-klintblue text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            Processing
          </button>
          <button
            onClick={() => handleStatusChange("shipped")}
            className={`px-2 py-1 rounded text-xs ${
              statusFilter === "shipped"
                ? "bg-klintblue text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            Shipped
          </button>
          <button
            onClick={() => handleStatusChange("delivered")}
            className={`px-2 py-1 rounded text-xs ${
              statusFilter === "delivered"
                ? "bg-klintblue text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            Delivered
          </button>
          <button
            onClick={() => handleStatusChange("cancelled")}
            className={`px-2 py-1 rounded text-xs ${
              statusFilter === "cancelled"
                ? "bg-klintblue text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            Cancelled
          </button>
        </div>
      </div>

      {/* Orders List - Compact */}
      <div className="bg-white rounded shadow-sm overflow-hidden">
        {filteredOrders.length === 0 ? (
          <div className="p-3 text-center">
            <p className="text-gray-500 text-xs">No orders found.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    ID
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Date
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Customer
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Status
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Total
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredOrders.map((order) => (
                  <tr key={order.id} className="hover:bg-gray-50">
                    <td className="px-3 py-2 whitespace-nowrap text-xs font-medium text-gray-900">
                      #{order.id}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500">
                      {new Date(order.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-900">
                      {order.customer_name || "Customer #" + order.user_id}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap">
                      <span
                        className={`px-1.5 py-0.5 inline-flex text-xs leading-4 font-medium rounded-full ${getStatusBadgeClass(
                          order.status
                        )}`}
                      >
                        {order.status}
                      </span>
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-900">
                      ${parseFloat(order.total_amount).toFixed(2)}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-xs font-medium">
                      <div className="flex items-center space-x-2">
                        <Link
                          to={`/vendor/orders/${order.id}`}
                          className="text-klintblue hover:text-klintAccent"
                        >
                          View
                        </Link>

                        {order.status === "pending" && (
                          <button
                            onClick={() =>
                              handleUpdateOrderStatus(order.id, "processing")
                            }
                            className="text-blue-600 hover:text-blue-900"
                          >
                            Process
                          </button>
                        )}

                        {order.status === "processing" && (
                          <button
                            onClick={() =>
                              handleUpdateOrderStatus(order.id, "shipped")
                            }
                            className="text-purple-600 hover:text-purple-900"
                          >
                            Ship
                          </button>
                        )}

                        {order.status === "shipped" && (
                          <button
                            onClick={() =>
                              handleUpdateOrderStatus(order.id, "delivered")
                            }
                            className="text-green-600 hover:text-green-900"
                          >
                            Deliver
                          </button>
                        )}

                        {(order.status === "pending" ||
                          order.status === "processing") && (
                          <button
                            onClick={() =>
                              handleUpdateOrderStatus(order.id, "cancelled")
                            }
                            className="text-red-600 hover:text-red-900"
                          >
                            Cancel
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}

export default VendorOrders;
