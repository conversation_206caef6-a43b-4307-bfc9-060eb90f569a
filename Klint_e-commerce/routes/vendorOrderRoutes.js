const express = require("express");
const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");
const {
  getOrdersForVendor,
  getOrderDetailsForVendor,
  updateOrderStatusForVendor,
} = require("../queries");

// Get all orders containing the vendor's products
router.get("/", authMiddleware(["vendor"]), async (req, res) => {
  try {
    const { status } = req.query;
    const orders = await getOrdersForVendor(req.user.userId, status);

    res.json({
      success: true,
      orders,
    });
  } catch (error) {
    console.error("Error getting vendor orders:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving orders",
      error: error.message,
    });
  }
});

// Get details of a specific order containing the vendor's products
router.get("/:orderId", authMiddleware(["vendor"]), async (req, res) => {
  try {
    const { orderId } = req.params;

    if (!orderId) {
      return res.status(400).json({
        success: false,
        message: "Order ID is required",
      });
    }

    const order = await getOrderDetailsForVendor(orderId, req.user.userId);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found or does not contain your products",
      });
    }

    res.json({
      success: true,
      order,
    });
  } catch (error) {
    console.error("Error getting order details:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving order details",
      error: error.message,
    });
  }
});

// Update order status
router.patch(
  "/:orderId/status",
  authMiddleware(["vendor"]),
  async (req, res) => {
    try {
      const { orderId } = req.params;
      const { status } = req.body;

      if (!orderId) {
        return res.status(400).json({
          success: false,
          message: "Order ID is required",
        });
      }

      if (!status) {
        return res.status(400).json({
          success: false,
          message: "Status is required",
        });
      }

      // Validate status
      const validStatuses = [
        "pending",
        "processing",
        "shipped",
        "delivered",
        "cancelled",
      ];
      if (!validStatuses.includes(status)) {
        return res.status(400).json({
          success: false,
          message: `Invalid status. Must be one of: ${validStatuses.join(
            ", "
          )}`,
        });
      }

      const updatedOrder = await updateOrderStatusForVendor(
        orderId,
        req.user.userId,
        status
      );

      res.json({
        success: true,
        message: "Order status updated successfully",
        order: updatedOrder,
      });
    } catch (error) {
      console.error("Error updating order status:", error);

      if (
        error.message === "Order not found or does not contain your products"
      ) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: "Error updating order status",
        error: error.message,
      });
    }
  }
);

module.exports = router;
