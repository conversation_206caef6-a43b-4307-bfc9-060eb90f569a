import { useState, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import api from "../services/api";

function SearchResults() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");

  const location = useLocation();
  const navigate = useNavigate();

  // Get search query from URL
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const query = params.get("q");

    if (query) {
      setSearchQuery(query);
      fetchSearchResults(query);
    } else {
      // Redirect to products page if no query
      navigate("/products");
    }
  }, [location.search, navigate]);

  // Fetch search results
  const fetchSearchResults = async (query) => {
    setLoading(true);
    try {
      console.log(`Searching for products with query: "${query}"`);
      const response = await api.products.search(query);

      if (response.success && response.products) {
        console.log(
          `Found ${response.products.length} products matching "${query}"`
        );

        // Filter products client-side to ensure they match the search query
        const filteredProducts = response.products.filter((product) => {
          const searchLower = query.toLowerCase();
          return (
            product.name.toLowerCase().includes(searchLower) ||
            (product.description &&
              product.description.toLowerCase().includes(searchLower)) ||
            (product.category_name &&
              product.category_name.toLowerCase().includes(searchLower))
          );
        });

        console.log(
          `After filtering: ${filteredProducts.length} products match the search criteria`
        );
        setProducts(filteredProducts);
        setError(null);
      } else {
        throw new Error("Invalid response format");
      }
    } catch (err) {
      console.error("Error searching products:", err);
      setError("Failed to search products. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Handle search form submission
  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="container-custom py-8">
        <h1 className="text-3xl font-bold mb-8">Search Results</h1>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-klintblue border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-lg">Searching products...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container-custom py-8">
      <h1 className="text-3xl font-bold mb-4">Search Results</h1>

      {/* Search form */}
      <form onSubmit={handleSearch} className="mb-8">
        <div className="flex">
          <input
            type="text"
            placeholder="Search for products..."
            className="flex-1 p-2 pl-4 border rounded-l-md"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <button
            type="submit"
            className="bg-klintblue text-white px-6 py-2 rounded-r-md"
          >
            Search
          </button>
        </div>
      </form>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
          <p className="mt-2 text-sm">
            Try searching for something else or{" "}
            <Link to="/products" className="underline text-red-700 font-medium">
              browse all products
            </Link>
            .
          </p>
        </div>
      )}

      {/* Search results */}
      {!error && (
        <div className="mb-4">
          <p className="text-gray-600">
            {products.length > 0
              ? `Found ${products.length} results for "${searchQuery}"`
              : `No results found for "${searchQuery}"`}
          </p>
        </div>
      )}

      {!error && products.length === 0 ? (
        <div className="bg-gray-100 p-8 rounded-lg text-center">
          <p className="text-lg text-gray-600 mb-4">
            No products found matching your search for "{searchQuery}".
          </p>
          <div className="mb-6">
            <p className="text-gray-600 mb-2">Suggestions:</p>
            <ul className="text-gray-600 text-sm list-disc list-inside mb-4">
              <li>Check the spelling of your search terms</li>
              <li>Try using more general keywords</li>
              <li>Try searching for a related product</li>
            </ul>
          </div>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link to="/products" className="btn-primary inline-block">
              Browse All Products
            </Link>
            <Link to="/categories" className="btn-secondary inline-block">
              Browse Categories
            </Link>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Product cards */}
          {products.map((product) => (
            <div
              key={product.id}
              className="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
            >
              <Link to={`/products/${product.id}`} className="block">
                <div className="h-48 bg-gray-200 flex items-center justify-center">
                  {product.images && product.images.length > 0 ? (
                    <img
                      src={
                        product.images.find((img) => img.is_primary)?.url ||
                        product.images[0].url
                      }
                      alt={product.name}
                      className="h-full w-full object-cover"
                      onError={(e) => {
                        console.log(
                          "Search result image failed to load:",
                          e.target.src
                        );
                        e.target.onerror = null;
                        e.target.src = "/placeholder.svg";
                      }}
                    />
                  ) : (
                    <span className="text-gray-500">No Image</span>
                  )}
                </div>
                <div className="p-4">
                  <h3 className="font-medium text-lg hover:text-klintblue transition-colors">
                    {product.name}
                  </h3>
                  <p className="text-gray-600 text-sm mb-2">
                    {product.category_name || "Uncategorized"}
                  </p>
                  <p className="font-bold text-klintblue">
                    ${parseFloat(product.price).toFixed(2)}
                  </p>
                </div>
              </Link>
              <div className="px-4 pb-4">
                <button
                  className="btn-primary w-full"
                  onClick={async (e) => {
                    e.preventDefault();
                    try {
                      const response = await api.cart.addItem(product.id, 1);
                      if (response.success) {
                        alert(`Added ${product.name} to cart!`);
                      }
                    } catch (err) {
                      console.error("Error adding product to cart:", err);
                      alert("Failed to add product to cart. Please try again.");
                    }
                  }}
                  disabled={product.stock_quantity <= 0}
                >
                  {product.stock_quantity > 0 ? "Add to Cart" : "Out of Stock"}
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default SearchResults;
