require("dotenv").config();

const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const rateLimit = require("express-rate-limit");
const morgan = require("morgan");
const pool = require("./config/db");
const authRoutes = require("./routes/authRoutes");
const productRoutes = require("./routes/productRoutes");
const catalogRoutes = require("./routes/catalogRoutes");
const cartRoutes = require("./routes/cartRoutes");
const wishlistRoutes = require("./routes/wishlistRoutes");
const orderRoutes = require("./routes/orderRoutes");
const vendorOrderRoutes = require("./routes/vendorOrderRoutes");

const app = express();

// Environment configuration
const isProduction = process.env.NODE_ENV === "production";
const isDevelopment = !isProduction;

// Base URLs based on environment
const BASE_URL = isProduction
  ? process.env.PRODUCTION_URL || "http://klint.geodhis.co.ke"
  : `http://localhost:${process.env.PORT || 5000}`;

// Log environment variables for debugging (remove sensitive info in production)
console.log("Starting server with environment:", {
  NODE_ENV: process.env.NODE_ENV || "development",
  PORT: process.env.PORT,
  DB_HOST: process.env.DB_HOST,
  DB_NAME: process.env.DB_NAME,
  BASE_URL: BASE_URL,
});

// CORS configuration
const corsOptions = {
  origin: "*",
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "Accept",
    "Origin",
    "X-Requested-With",
  ],
  credentials: true,
  exposedHeaders: ["Authorization", "retry-after"],
};

if (isProduction) {
  app.set("trust proxy", 1);
}

app.use(cors(corsOptions));

// Helmet configuration
app.use(
  helmet({
    contentSecurityPolicy: false,
    crossOriginEmbedderPolicy: false,
    crossOriginResourcePolicy: { policy: "cross-origin" },
    dnsPrefetchControl: { allow: true },
    frameguard: { action: "deny" },
    hidePoweredBy: true,
    hsts: isProduction
      ? {
          maxAge: 31536000,
          includeSubDomains: true,
          preload: true,
        }
      : false,
    ieNoOpen: true,
    noSniff: true,
    referrerPolicy: { policy: "no-referrer" },
    xssFilter: true,
  })
);

// Body parsing
app.use(
  express.json({
    limit: "50mb",
    verify: (req, res, buf) => {
      req.rawBody = buf.toString();
    },
  })
);
app.use(
  express.urlencoded({
    extended: true,
    limit: "50mb",
  })
);

// Logging
app.use(
  morgan(isDevelopment ? "dev" : "combined", {
    skip: (req, res) => req.path === "/api/health" && res.statusCode === 200,
  })
);

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: isDevelopment ? 1000 : 500,
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    status: 429,
    success: false,
    message: "Too many requests, please try again later",
  },
  skip: (req) => req.path === "/api/health" || isDevelopment,
  keyGenerator: (req) =>
    req.headers["x-forwarded-for"] || req.ip || req.connection.remoteAddress,
  handler: (req, res) => {
    res.status(429).json({
      success: false,
      message: "Too many requests, please try again later",
      retryAfter: Math.ceil(limiter.windowMs / 1000),
    });
  },
});

app.use("/api", limiter);

// Simple test route for debugging
app.get("/api/test", (req, res) => {
  res.json({
    success: true,
    message: "API is running",
    timestamp: new Date().toISOString(),
  });
});

// Database test endpoint
app.get("/api/test-db", async (req, res) => {
  try {
    const [result] = await pool.query("SELECT 1 AS test");
    res.json({
      success: true,
      message: "Database connection successful",
      result: result[0],
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Database test error:", error);
    res.status(500).json({
      success: false,
      message: "Database connection failed",
      error: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});

// Routes
app.use("/api/auth", authRoutes);
app.use("/api/products", productRoutes);
app.use("/api/catalog", catalogRoutes);
app.use("/api/cart", cartRoutes);
app.use("/api/wishlist", wishlistRoutes);
app.use("/api/orders", orderRoutes);
app.use("/api/vendor/orders", vendorOrderRoutes);

// Database connection check
const checkDatabaseConnection = async () => {
  try {
    const [result] = await pool.query("SELECT 1 AS test");
    console.log("Database connection successful:", result[0]);
    return true;
  } catch (error) {
    console.error("Database connection test failed:", {
      error: error.message,
      code: error.code,
      stack: error.stack,
    });
    return false;
  }
};

// Health check
app.get("/api/health", async (req, res) => {
  try {
    const startTime = Date.now();
    const isConnected = await checkDatabaseConnection();
    const responseTime = Date.now() - startTime;

    // Get database configuration (excluding sensitive info)
    const dbConfig = {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USER,
      database: process.env.DB_NAME,
    };

    if (!isConnected) {
      throw new Error("Database connection test failed");
    }

    res.json({
      success: true,
      message: "Server is healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || "development",
      baseUrl: BASE_URL,
      database: {
        status: "connected",
        responseTime: `${responseTime}ms`,
        config: dbConfig,
      },
    });
  } catch (error) {
    console.error("Health check failed:", error);
    res.status(503).json({
      success: false,
      message: "Server is unhealthy",
      error: isDevelopment ? error.message : "Database connection failed",
      timestamp: new Date().toISOString(),
      database: {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        user: process.env.DB_USER,
        database: process.env.DB_NAME,
      },
    });
  }
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: "Route not found",
    path: req.originalUrl,
    timestamp: new Date().toISOString(),
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error("Error:", {
    timestamp: new Date().toISOString(),
    path: req.originalUrl,
    method: req.method,
    error: err.message,
    stack: isDevelopment ? err.stack : undefined,
    body: isDevelopment ? req.body : undefined,
    ip: req.ip,
    userAgent: req.get("user-agent"),
  });

  if (err.name === "UnauthorizedError") {
    return res.status(401).json({
      success: false,
      message: "Invalid or expired token",
      timestamp: new Date().toISOString(),
    });
  }

  if (err.name === "ValidationError") {
    return res.status(400).json({
      success: false,
      message: err.message,
      timestamp: new Date().toISOString(),
    });
  }

  if (err.code === "ECONNREFUSED") {
    return res.status(503).json({
      success: false,
      message: isDevelopment
        ? "Database connection failed - Check if your database is running"
        : "Database connection failed",
      timestamp: new Date().toISOString(),
    });
  }

  if (err.type === "entity.too.large") {
    return res.status(413).json({
      success: false,
      message: "Request entity too large",
      timestamp: new Date().toISOString(),
    });
  }

  res.status(err.status || 500).json({
    success: false,
    message: isDevelopment ? err.message : "Something went wrong!",
    timestamp: new Date().toISOString(),
    ...(isDevelopment && { stack: err.stack }),
  });
});

// Server startup
const startServer = async () => {
  const PORT = process.env.PORT || 5000;
  let server;

  try {
    // Test database connection before starting
    const isConnected = await checkDatabaseConnection();
    if (!isConnected) {
      console.error("Failed to connect to database. Server will not start.");
      process.exit(1);
    }

    server = app.listen(PORT, () => {
      console.log(`
Server started successfully:
- Port: ${PORT}
- Environment: ${process.env.NODE_ENV || "development"}
- Health Check: ${BASE_URL}/api/health
- Test Route: ${BASE_URL}/api/test
- API Base URL: ${BASE_URL}/api
            `);
    });

    // Graceful shutdown
    const gracefulShutdown = async (signal) => {
      console.log(`${signal} received. Starting graceful shutdown...`);

      try {
        await new Promise((resolve) => server.close(resolve));
        console.log("HTTP server closed");

        await pool.end();
        console.log("Database connections closed");

        process.exit(0);
      } catch (err) {
        console.error("Error during shutdown:", err);
        process.exit(1);
      }
    };

    process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
    process.on("SIGINT", () => gracefulShutdown("SIGINT"));
  } catch (err) {
    console.error("Server startup failed:", {
      error: err.message,
      stack: err.stack,
    });
    process.exit(1);
  }
};

// Global error handlers
process.on("unhandledRejection", (err) => {
  console.error("Unhandled Promise Rejection:", {
    message: err.message,
    stack: err.stack,
    timestamp: new Date().toISOString(),
  });
});

process.on("uncaughtException", (err) => {
  console.error("Uncaught Exception:", {
    message: err.message,
    stack: err.stack,
    timestamp: new Date().toISOString(),
  });
  process.exit(1);
});

// Start the server
startServer();
