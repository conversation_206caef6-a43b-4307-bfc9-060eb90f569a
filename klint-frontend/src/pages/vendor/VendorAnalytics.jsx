import React, { useState, useEffect } from "react";
import api from "../../services/api";
import VendorNav from "../../components/vendor/VendorNav";

function VendorAnalytics() {
  const [timeRange, setTimeRange] = useState("month");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [analytics, setAnalytics] = useState({
    totalSales: 0,
    totalOrders: 0,
    averageOrderValue: 0,
    topProducts: [],
    salesByStatus: {
      pending: 0,
      processing: 0,
      shipped: 0,
      delivered: 0,
      cancelled: 0,
    },
    salesByDay: [],
  });

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    setLoading(true);
    try {
      // In a real application, you would have a dedicated API endpoint for analytics
      // For now, we'll simulate it by fetching orders and calculating analytics
      const ordersResponse = await api.vendor.getOrders();

      if (ordersResponse.success) {
        const orders = ordersResponse.orders || [];

        // Filter orders based on time range
        const filteredOrders = filterOrdersByTimeRange(orders, timeRange);

        // Calculate analytics
        const calculatedAnalytics = calculateAnalytics(filteredOrders);
        setAnalytics(calculatedAnalytics);
      } else {
        setError("Failed to fetch analytics data");
      }
    } catch (error) {
      console.error("Error fetching analytics:", error);
      setError("An error occurred while fetching analytics data");
    } finally {
      setLoading(false);
    }
  };

  const filterOrdersByTimeRange = (orders, range) => {
    const now = new Date();
    let startDate;

    switch (range) {
      case "week":
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 7);
        break;
      case "month":
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 1);
        break;
      case "year":
        startDate = new Date(now);
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate = new Date(0); // All time
    }

    return orders.filter((order) => new Date(order.created_at) >= startDate);
  };

  const calculateAnalytics = (orders) => {
    // Total sales and orders
    const totalSales = orders.reduce(
      (sum, order) => sum + parseFloat(order.total_amount),
      0
    );
    const totalOrders = orders.length;
    const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;

    // Sales by status
    const salesByStatus = {
      pending: 0,
      processing: 0,
      shipped: 0,
      delivered: 0,
      cancelled: 0,
    };

    orders.forEach((order) => {
      if (salesByStatus[order.status] !== undefined) {
        salesByStatus[order.status] += parseFloat(order.total_amount);
      }
    });

    // Top products
    const productSales = {};
    orders.forEach((order) => {
      if (order.items && Array.isArray(order.items)) {
        order.items.forEach((item) => {
          const productId = item.product_id;
          if (!productSales[productId]) {
            productSales[productId] = {
              id: productId,
              name: item.product_name || `Product #${productId}`,
              quantity: 0,
              revenue: 0,
            };
          }
          productSales[productId].quantity += item.quantity;
          productSales[productId].revenue +=
            parseFloat(item.price_at_time) * item.quantity;
        });
      }
    });

    const topProducts = Object.values(productSales)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    // Sales by day (for the chart)
    const salesByDay = {};
    orders.forEach((order) => {
      const date = new Date(order.created_at).toLocaleDateString();
      if (!salesByDay[date]) {
        salesByDay[date] = 0;
      }
      salesByDay[date] += parseFloat(order.total_amount);
    });

    const salesByDayArray = Object.entries(salesByDay)
      .map(([date, amount]) => ({
        date,
        amount,
      }))
      .sort((a, b) => new Date(a.date) - new Date(b.date));

    return {
      totalSales,
      totalOrders,
      averageOrderValue,
      topProducts,
      salesByStatus,
      salesByDay: salesByDayArray,
    };
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="w-16 h-16 border-4 border-klintblue border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-6 rounded-lg text-center">
        <p className="text-red-600 mb-4">{error}</p>
        <button
          onClick={() => fetchAnalytics()}
          className="bg-klintblue text-white px-4 py-2 rounded-md hover:bg-klintAccent transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="container-custom py-4">
      <VendorNav />

      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 gap-2">
        <h1 className="text-base font-bold">Sales Analytics</h1>
        <div className="flex flex-wrap gap-1">
          <button
            onClick={() => setTimeRange("week")}
            className={`px-2 py-1 rounded text-xs ${
              timeRange === "week"
                ? "bg-klintblue text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            Last Week
          </button>
          <button
            onClick={() => setTimeRange("month")}
            className={`px-2 py-1 rounded text-xs ${
              timeRange === "month"
                ? "bg-klintblue text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            Last Month
          </button>
          <button
            onClick={() => setTimeRange("year")}
            className={`px-2 py-1 rounded text-xs ${
              timeRange === "year"
                ? "bg-klintblue text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            Last Year
          </button>
          <button
            onClick={() => setTimeRange("all")}
            className={`px-2 py-1 rounded text-xs ${
              timeRange === "all"
                ? "bg-klintblue text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            All Time
          </button>
        </div>
      </div>

      {/* Summary Cards - Compact */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-3">
        <div className="bg-white rounded shadow-sm p-3">
          <h2 className="text-sm font-semibold mb-1">Total Sales</h2>
          <p className="text-xl font-bold text-klintblue">
            ${analytics.totalSales.toFixed(2)}
          </p>
          <p className="text-gray-500 text-xs mt-1">
            {timeRange === "week"
              ? "Last 7 days"
              : timeRange === "month"
              ? "Last 30 days"
              : timeRange === "year"
              ? "Last 365 days"
              : "All time"}
          </p>
        </div>

        <div className="bg-white rounded shadow-sm p-3">
          <h2 className="text-sm font-semibold mb-1">Total Orders</h2>
          <p className="text-xl font-bold text-klintblue">
            {analytics.totalOrders}
          </p>
          <p className="text-gray-500 text-xs mt-1">
            {timeRange === "week"
              ? "Last 7 days"
              : timeRange === "month"
              ? "Last 30 days"
              : timeRange === "year"
              ? "Last 365 days"
              : "All time"}
          </p>
        </div>

        <div className="bg-white rounded shadow-sm p-3">
          <h2 className="text-sm font-semibold mb-1">Average Order Value</h2>
          <p className="text-xl font-bold text-klintblue">
            ${analytics.averageOrderValue.toFixed(2)}
          </p>
          <p className="text-gray-500 text-xs mt-1">
            {timeRange === "week"
              ? "Last 7 days"
              : timeRange === "month"
              ? "Last 30 days"
              : timeRange === "year"
              ? "Last 365 days"
              : "All time"}
          </p>
        </div>
      </div>

      {/* Sales by Status - Compact */}
      <div className="bg-white rounded shadow-sm p-3 mb-3">
        <h2 className="text-sm font-semibold mb-2">Sales by Order Status</h2>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
          <div className="bg-yellow-50 rounded p-2">
            <h3 className="text-xs font-medium text-yellow-800 mb-1">
              Pending
            </h3>
            <p className="text-sm font-bold">
              ${analytics.salesByStatus.pending.toFixed(2)}
            </p>
          </div>

          <div className="bg-blue-50 rounded p-2">
            <h3 className="text-xs font-medium text-blue-800 mb-1">
              Processing
            </h3>
            <p className="text-sm font-bold">
              ${analytics.salesByStatus.processing.toFixed(2)}
            </p>
          </div>

          <div className="bg-purple-50 rounded p-2">
            <h3 className="text-xs font-medium text-purple-800 mb-1">
              Shipped
            </h3>
            <p className="text-sm font-bold">
              ${analytics.salesByStatus.shipped.toFixed(2)}
            </p>
          </div>

          <div className="bg-green-50 rounded p-2">
            <h3 className="text-xs font-medium text-green-800 mb-1">
              Delivered
            </h3>
            <p className="text-sm font-bold">
              ${analytics.salesByStatus.delivered.toFixed(2)}
            </p>
          </div>

          <div className="bg-red-50 rounded p-2">
            <h3 className="text-xs font-medium text-red-800 mb-1">Cancelled</h3>
            <p className="text-sm font-bold">
              ${analytics.salesByStatus.cancelled.toFixed(2)}
            </p>
          </div>
        </div>
      </div>

      {/* Top Products - Compact */}
      <div className="bg-white rounded shadow-sm p-3 mb-3">
        <h2 className="text-sm font-semibold mb-2">Top Selling Products</h2>
        {analytics.topProducts.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Product
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Units
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Revenue
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {analytics.topProducts.map((product) => (
                  <tr key={product.id} className="hover:bg-gray-50">
                    <td className="px-3 py-2 whitespace-nowrap text-xs font-medium text-gray-900">
                      {product.name}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500">
                      {product.quantity}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-900">
                      ${product.revenue.toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-gray-500 text-center py-2 text-xs">
            No product sales data available for the selected period.
          </p>
        )}
      </div>

      {/* Sales Chart (simplified representation) - Compact */}
      <div className="bg-white rounded shadow-sm p-3">
        <h2 className="text-sm font-semibold mb-2">Sales Trend</h2>
        {analytics.salesByDay.length > 0 ? (
          <div className="h-40 flex items-end space-x-1">
            {analytics.salesByDay.map((day, index) => {
              const maxSale = Math.max(
                ...analytics.salesByDay.map((d) => d.amount)
              );
              const height = (day.amount / maxSale) * 100;

              return (
                <div key={index} className="flex flex-col items-center flex-1">
                  <div
                    className="w-full bg-klintblue rounded-t-sm"
                    style={{ height: `${height}%` }}
                  ></div>
                  <p className="text-[10px] text-gray-500 mt-1 transform -rotate-45 origin-top-left truncate max-w-[30px]">
                    {day.date}
                  </p>
                </div>
              );
            })}
          </div>
        ) : (
          <p className="text-gray-500 text-center py-2 text-xs">
            No sales data available for the selected period.
          </p>
        )}
        <p className="text-gray-500 text-[10px] text-center mt-4">
          Note: This is a simplified chart representation.
        </p>
      </div>
    </div>
  );
}

export default VendorAnalytics;
