import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { useCart } from "../context/CartContext";

function Cart() {
  const {
    cartItems,
    loading,
    error,
    fetchCart,
    removeItem,
    updateItemQuantity,
    clearCart,
    calculateTotals,
  } = useCart();

  // Fetch cart data on component mount
  useEffect(() => {
    fetchCart();
  }, []);

  // Handle quantity decrease
  const handleDecreaseQuantity = async (item) => {
    if (item.quantity > 1) {
      await updateItemQuantity(item.product_id, item.quantity - 1);
    }
  };

  // Handle quantity increase
  const handleIncreaseQuantity = async (item) => {
    if (item.quantity < item.stock_quantity) {
      await updateItemQuantity(item.product_id, item.quantity + 1);
    }
  };

  // Handle remove item
  const handleRemoveItem = async (productId) => {
    if (
      window.confirm(
        "Are you sure you want to remove this item from your cart?"
      )
    ) {
      await removeItem(productId);
    }
  };

  // Handle clear cart
  const handleClearCart = async () => {
    if (window.confirm("Are you sure you want to clear your cart?")) {
      await clearCart();
    }
  };

  // Get cart totals
  const { subtotal, shipping, tax, total } = calculateTotals();

  return (
    <div className="container-custom py-8">
      <h1 className="text-3xl font-bold mb-8">Your Cart</h1>

      {/* Loading state */}
      {loading && (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-klintblue border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-lg">Loading your cart...</p>
          </div>
        </div>
      )}

      {/* Error state */}
      {error && !loading && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
          <button onClick={fetchCart} className="mt-2 text-red-700 underline">
            Try again
          </button>
        </div>
      )}

      {/* Error notification */}
      {error && (
        <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded shadow-lg z-50">
          <p>{error}</p>
          <button
            onClick={() => fetchCart()}
            className="mt-2 text-red-700 underline"
          >
            Refresh cart
          </button>
        </div>
      )}

      {/* Cart content */}
      {!loading && !error && (
        <>
          {cartItems.length > 0 ? (
            <div className="flex flex-col lg:flex-row gap-8">
              {/* Cart Items */}
              <div className="lg:w-2/3">
                <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="py-4 px-6 text-left">Product</th>
                        <th className="py-4 px-6 text-center">Quantity</th>
                        <th className="py-4 px-6 text-right">Price</th>
                        <th className="py-4 px-6 text-right">Total</th>
                        <th className="py-4 px-6"></th>
                      </tr>
                    </thead>
                    <tbody className="divide-y">
                      {cartItems.map((item) => (
                        <tr key={item.id}>
                          <td className="py-4 px-6">
                            <div className="flex items-center">
                              {/* Product image */}
                              <div className="h-16 w-16 bg-gray-200 rounded-md mr-4 overflow-hidden">
                                {item.images && item.images.length > 0 ? (
                                  <img
                                    src={
                                      item.images.find((img) => img.is_primary)
                                        ?.url || item.images[0].url
                                    }
                                    alt={item.name}
                                    className="h-full w-full object-cover"
                                  />
                                ) : (
                                  <div className="h-full w-full bg-gray-200 flex items-center justify-center text-gray-400">
                                    No image
                                  </div>
                                )}
                              </div>
                              <div>
                                <h3 className="font-medium">{item.name}</h3>
                              </div>
                            </div>
                          </td>
                          <td className="py-4 px-6">
                            <div className="flex justify-center">
                              <div className="flex items-center">
                                <button
                                  className="border rounded-l-md px-3 py-1 hover:bg-gray-100"
                                  onClick={() => handleDecreaseQuantity(item)}
                                  disabled={item.quantity <= 1}
                                >
                                  -
                                </button>
                                <span className="border-t border-b px-4 py-1">
                                  {item.quantity}
                                </span>
                                <button
                                  className="border rounded-r-md px-3 py-1 hover:bg-gray-100"
                                  onClick={() => handleIncreaseQuantity(item)}
                                  disabled={
                                    item.quantity >= item.stock_quantity
                                  }
                                >
                                  +
                                </button>
                              </div>
                            </div>
                          </td>
                          <td className="py-4 px-6 text-right">
                            ${parseFloat(item.price).toFixed(2)}
                          </td>
                          <td className="py-4 px-6 text-right">
                            $
                            {(parseFloat(item.price) * item.quantity).toFixed(
                              2
                            )}
                          </td>
                          <td className="py-4 px-6 text-right">
                            <button
                              className="text-red-500 hover:text-red-700"
                              onClick={() => handleRemoveItem(item.product_id)}
                            >
                              <span>×</span>
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                <div className="mt-6 flex justify-between">
                  <Link to="/products" className="btn-secondary">
                    Continue Shopping
                  </Link>
                  <button
                    className="text-red-500 hover:text-red-700"
                    onClick={handleClearCart}
                  >
                    Clear Cart
                  </button>
                </div>
              </div>

              {/* Order Summary */}
              <div className="lg:w-1/3">
                <div className="bg-white p-6 rounded-lg shadow-sm">
                  <h2 className="text-xl font-bold mb-6">Order Summary</h2>

                  <div className="space-y-4 mb-6">
                    <div className="flex justify-between">
                      <span>Subtotal</span>
                      <span>${subtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Shipping</span>
                      <span>${shipping.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tax</span>
                      <span>${tax.toFixed(2)}</span>
                    </div>
                    <div className="border-t pt-4 font-bold flex justify-between">
                      <span>Total</span>
                      <span>${total.toFixed(2)}</span>
                    </div>
                  </div>

                  <button className="btn-primary w-full">
                    Proceed to Checkout
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <h2 className="text-2xl font-bold mb-4">Your cart is empty</h2>
              <p className="mb-8">
                Looks like you haven't added any products to your cart yet.
              </p>
              <Link to="/products" className="btn-primary">
                Start Shopping
              </Link>
            </div>
          )}
        </>
      )}
    </div>
  );
}

export default Cart;
