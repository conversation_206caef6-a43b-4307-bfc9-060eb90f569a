import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import api from "../services/api";

function Categories() {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [productCounts, setProductCounts] = useState({});

  useEffect(() => {
    // Fetch categories from API
    const fetchCategories = async () => {
      setLoading(true);
      try {
        const response = await api.categories.getAll();

        if (response.success && response.categories) {
          // Format categories with default values
          const formattedCategories = response.categories.map((category) => ({
            ...category,
            // Add a default description if not provided
            description:
              category.description ||
              `Products in the ${category.name} category`,
          }));

          setCategories(formattedCategories);

          // Fetch product counts for each category
          await fetchProductCounts(formattedCategories);

          setError(null);
        } else {
          throw new Error("Invalid response format");
        }
      } catch (err) {
        console.error("Error fetching categories:", err);
        setError("Failed to load categories. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    // Fetch product counts for all categories
    const fetchProductCounts = async (categoriesList) => {
      try {
        const counts = {};

        // Fetch product counts for each category in parallel
        await Promise.all(
          categoriesList.map(async (category) => {
            try {
              const response = await api.categories.getProductCount(
                category.id
              );
              if (response.success && response.count !== undefined) {
                counts[category.id] = response.count;
              } else {
                counts[category.id] = 0;
              }
            } catch (err) {
              console.error(
                `Error fetching product count for category ${category.id}:`,
                err
              );
              counts[category.id] = 0;
            }
          })
        );

        setProductCounts(counts);
      } catch (err) {
        console.error("Error fetching product counts:", err);
      }
    };

    fetchCategories();
  }, []);

  // Show loading state
  if (loading && categories.length === 0) {
    return (
      <div className="container-custom py-8">
        <h1 className="text-3xl font-bold mb-8">Shop by Category</h1>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-klintblue border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-lg">Loading categories...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container-custom py-8">
      <h1 className="text-3xl font-bold mb-8">Shop by Category</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      {categories.length === 0 && !loading ? (
        <div className="bg-gray-100 p-8 rounded-lg text-center">
          <p className="text-lg text-gray-600">No categories found.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {categories.map((category) => (
            <Link
              key={category.id}
              to={`/products?category=${category.id}`}
              className="block group"
            >
              <div className="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                <div className="h-48 bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-500">{category.name}</span>
                </div>
                <div className="p-4">
                  <h2 className="text-xl font-medium mb-2 group-hover:text-klintblue transition-colors">
                    {category.name}
                  </h2>
                  <p className="text-gray-600 text-sm mb-2">
                    {category.description}
                  </p>
                  <p className="text-sm text-klintblue">
                    {productCounts[category.id] || 0} Products
                  </p>
                </div>
              </div>
            </Link>
          ))}
        </div>
      )}

    </div>
  );
}

export default Categories;
