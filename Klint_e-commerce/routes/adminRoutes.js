const express = require("express");
const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");
const {
  getAllUsers,
  getAllProducts,
  getAllOrders,
  getAdminStats,
  updateUserStatus,
  updateProductStatus,
  updateCategory,
  deleteCategory,
  createCategory,
  getCategories,
} = require("../queries");

// Middleware to check if user is an admin
const isAdmin = (req, res, next) => {
  if (req.user.role !== "admin") {
    return res.status(403).json({
      success: false,
      message: "Access denied. Admin privileges required.",
    });
  }
  next();
};

// Get admin dashboard statistics
router.get("/stats", authMiddleware(["admin"]), isAdmin, async (req, res) => {
  try {
    const stats = await getAdminStats();

    res.json({
      success: true,
      stats,
    });
  } catch (error) {
    console.error("Error getting admin stats:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving dashboard statistics",
      error: error.message,
    });
  }
});

// User Management Routes

// Get all users with filtering and pagination
router.get("/users", authMiddleware(["admin"]), isAdmin, async (req, res) => {
  try {
    console.log("Admin users endpoint called with query:", req.query);
    console.log("User role:", req.user?.role);

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const filters = {
      role: req.query.role,
      is_active:
        req.query.is_active !== undefined
          ? req.query.is_active === "true"
          : undefined,
      search: req.query.search,
    };

    console.log("Filters applied:", filters);

    const result = await getAllUsers(page, limit, filters);

    console.log("Users result:", {
      userCount: result.users?.length,
      pagination: result.pagination,
    });

    res.json({
      success: true,
      ...result,
    });
  } catch (error) {
    console.error("Error getting users:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving users",
      error: error.message,
    });
  }
});

// Update user status (activate/deactivate)
router.patch(
  "/users/:id/status",
  authMiddleware(["admin"]),
  isAdmin,
  async (req, res) => {
    try {
      const { is_active } = req.body;
      const userId = req.params.id;

      if (is_active === undefined) {
        return res.status(400).json({
          success: false,
          message: "is_active field is required",
        });
      }

      const user = await updateUserStatus(userId, is_active);

      res.json({
        success: true,
        message: `User ${is_active ? "activated" : "deactivated"} successfully`,
        user,
      });
    } catch (error) {
      console.error("Error updating user status:", error);
      res.status(500).json({
        success: false,
        message: "Error updating user status",
        error: error.message,
      });
    }
  }
);

// Product Management Routes

// Get all products with filtering and pagination
router.get(
  "/products",
  authMiddleware(["admin"]),
  isAdmin,
  async (req, res) => {
    try {
      console.log("Admin products endpoint called with query:", req.query);
      console.log("User role:", req.user?.role);

      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      const filters = {
        vendor_id: req.query.vendor_id,
        category_id: req.query.category_id,
        is_active:
          req.query.is_active !== undefined
            ? req.query.is_active === "true"
            : undefined,
        search: req.query.search,
      };

      console.log("Filters applied:", filters);

      const result = await getAllProducts(page, limit, filters);

      console.log("Products result:", {
        productCount: result.products?.length,
        pagination: result.pagination,
      });

      res.json({
        success: true,
        ...result,
      });
    } catch (error) {
      console.error("Error getting products:", error);
      res.status(500).json({
        success: false,
        message: "Error retrieving products",
        error: error.message,
      });
    }
  }
);

// Update product status (activate/deactivate)
router.patch(
  "/products/:id/status",
  authMiddleware(["admin"]),
  isAdmin,
  async (req, res) => {
    try {
      const { is_active } = req.body;
      const productId = req.params.id;

      if (is_active === undefined) {
        return res.status(400).json({
          success: false,
          message: "is_active field is required",
        });
      }

      const product = await updateProductStatus(productId, is_active);

      res.json({
        success: true,
        message: `Product ${
          is_active ? "activated" : "deactivated"
        } successfully`,
        product,
      });
    } catch (error) {
      console.error("Error updating product status:", error);
      res.status(500).json({
        success: false,
        message: "Error updating product status",
        error: error.message,
      });
    }
  }
);

// Order Management Routes

// Get all orders with filtering and pagination
router.get("/orders", authMiddleware(["admin"]), isAdmin, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const filters = {
      status: req.query.status,
      payment_status: req.query.payment_status,
      user_id: req.query.user_id,
      date_from: req.query.date_from,
      date_to: req.query.date_to,
    };

    const result = await getAllOrders(page, limit, filters);

    res.json({
      success: true,
      ...result,
    });
  } catch (error) {
    console.error("Error getting orders:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving orders",
      error: error.message,
    });
  }
});

// Category Management Routes

// Get all categories
router.get(
  "/categories",
  authMiddleware(["admin"]),
  isAdmin,
  async (req, res) => {
    try {
      const categories = await getCategories();

      res.json({
        success: true,
        categories,
      });
    } catch (error) {
      console.error("Error getting categories:", error);
      res.status(500).json({
        success: false,
        message: "Error retrieving categories",
        error: error.message,
      });
    }
  }
);

// Create a new category
router.post(
  "/categories",
  authMiddleware(["admin"]),
  isAdmin,
  async (req, res) => {
    try {
      const { name, description, parent_id } = req.body;

      if (!name) {
        return res.status(400).json({
          success: false,
          message: "Category name is required",
        });
      }

      const category = await createCategory({ name, description, parent_id });

      res.status(201).json({
        success: true,
        message: "Category created successfully",
        category,
      });
    } catch (error) {
      console.error("Error creating category:", error);
      res.status(500).json({
        success: false,
        message: "Error creating category",
        error: error.message,
      });
    }
  }
);

// Update a category
router.put(
  "/categories/:id",
  authMiddleware(["admin"]),
  isAdmin,
  async (req, res) => {
    try {
      const { name, description, parent_id } = req.body;
      const categoryId = req.params.id;

      if (!name) {
        return res.status(400).json({
          success: false,
          message: "Category name is required",
        });
      }

      const category = await updateCategory(categoryId, {
        name,
        description,
        parent_id,
      });

      res.json({
        success: true,
        message: "Category updated successfully",
        category,
      });
    } catch (error) {
      console.error("Error updating category:", error);
      res.status(500).json({
        success: false,
        message: "Error updating category",
        error: error.message,
      });
    }
  }
);

// Delete a category
router.delete(
  "/categories/:id",
  authMiddleware(["admin"]),
  isAdmin,
  async (req, res) => {
    try {
      const categoryId = req.params.id;

      await deleteCategory(categoryId);

      res.json({
        success: true,
        message: "Category deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting category:", error);

      if (error.message.includes("Cannot delete category")) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: "Error deleting category",
        error: error.message,
      });
    }
  }
);

module.exports = router;
