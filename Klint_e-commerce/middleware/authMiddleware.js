const { verifyToken, TokenError } = require("../utils/jwtUtils"); // Import the verifyToken and TokenError functions

const authMiddleware = (roles) => (req, res, next) => {
  const authHeader = req.header("Authorization");
  console.log("Auth header received:", authHeader); // Debug log

  const token = authHeader?.replace("Bearer ", ""); // Note the space after Bearer
  console.log("Extracted token:", token); // Debug log

  if (!token) {
    return res
      .status(401)
      .json({ message: "Access denied. No token provided." });
  }

  try {
    const decoded = verifyToken(token);
    console.log("Decoded token payload:", decoded); // Debug log

    if (!roles.includes(decoded.role)) {
      return res
        .status(403)
        .json({ message: "Access denied. Insufficient permissions." });
    }

    req.user = decoded;
    next();
  } catch (error) {
    console.error("Token verification error:", {
      error: error.message,
      tokenReceived: token,
      authHeader: authHeader,
    });

    if (error instanceof TokenError) {
      return res.status(401).json({
        success: false,
        message: error.message,
        errorType: error.name,
        debug:
          process.env.NODE_ENV !== "production"
            ? {
                tokenReceived: token?.substring(0, 10) + "...",
                authHeader: authHeader,
              }
            : undefined,
      });
    }

    res.status(400).json({
      success: false,
      message: "Invalid token.",
    });
  }
};

module.exports = authMiddleware;
