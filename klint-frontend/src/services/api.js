// Base API configuration
const API_URL = import.meta.env.VITE_API_URL || "https://klint.geodhis.co.ke";

// Helper function for making API requests
async function fetchApi(endpoint, options = {}) {
  const url = `${API_URL}${endpoint}`;

  // Default headers - don't set Content-Type for FormData
  const headers = {
    ...options.headers,
  };

  // Only set Content-Type if not FormData (browser will set it automatically for FormData)
  if (!(options.body instanceof FormData)) {
    headers["Content-Type"] = "application/json";
  }

  // Add auth token if available
  const token = localStorage.getItem("authToken");
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const config = {
    ...options,
    headers,
  };

  try {
    const response = await fetch(url, config);

    // Handle 401 Unauthorized
    if (response.status === 401) {
      // Clear token and redirect to login
      localStorage.removeItem("authToken");
      window.location.href = "/login";
      return null;
    }

    const data = await response.json();

    if (!response.ok) {
      console.error("API Error Response:", data);
      console.error("Response status:", response.status);
      console.error("Response headers:", response.headers);
      throw new Error(
        data.message || `HTTP ${response.status}: Something went wrong`
      );
    }

    // Log API responses for debugging image issues
    if (
      endpoint.includes("/products/") ||
      endpoint.includes("/catalog/products")
    ) {
      console.log(`API Response for ${endpoint}:`, data);

      // If this is a product detail response, log image data specifically
      if (data.product && data.product.images) {
        console.log("Product images from API:", data.product.images);
      } else if (data.products) {
        // Log first product's images as a sample
        const sampleProduct = data.products[0];
        if (sampleProduct && sampleProduct.images) {
          console.log("Sample product images from API:", sampleProduct.images);
        }
      }
    }

    return data;
  } catch (error) {
    console.error("API Error:", error);
    throw error;
  }
}

export default {
  // Auth endpoints
  auth: {
    login: (credentials) =>
      fetchApi("/api/auth/login", {
        method: "POST",
        body: JSON.stringify(credentials),
      }),
    register: (userData) =>
      fetchApi("/api/auth/register", {
        method: "POST",
        body: JSON.stringify(userData),
      }),
    logout: () => {
      localStorage.removeItem("authToken");
      return Promise.resolve();
    },
    // Get current user profile
    getProfile: () => fetchApi("/api/auth/me"),
    // Update user profile
    updateProfile: (userData) =>
      fetchApi("/api/auth/profile", {
        method: "PUT",
        body: JSON.stringify(userData),
      }),
  },

  // Products endpoints
  products: {
    getAll: (params = {}) => {
      const queryParams = new URLSearchParams(params).toString();
      return fetchApi(`/api/catalog/products?${queryParams}`);
    },
    getById: (id) => fetchApi(`/api/products/${id}`),
    getByCategory: (categoryId) =>
      fetchApi(`/api/catalog/categories/${categoryId}/products`),
    search: (query) => {
      // Use the catalog products endpoint with a search parameter
      return fetchApi(
        `/api/catalog/products?search=${encodeURIComponent(query)}`
      );
    },
  },

  // Categories endpoints
  categories: {
    getAll: () => fetchApi("/api/catalog/categories"),
    getById: (id) => fetchApi(`/api/categories/${id}`),
    getProductCount: async (categoryId) => {
      try {
        const response = await fetchApi(
          `/api/catalog/categories/${categoryId}/products`
        );
        // If the response has a count property, use it
        if (response.count !== undefined) {
          return response;
        }
        // Otherwise, count the products array length
        if (response.products && Array.isArray(response.products)) {
          return {
            ...response,
            count: response.products.length,
          };
        }
        // Default fallback
        return { success: true, count: 0 };
      } catch (error) {
        console.error(
          `Error getting product count for category ${categoryId}:`,
          error
        );
        return { success: false, count: 0 };
      }
    },
  },

  // Cart endpoints
  cart: {
    get: () => fetchApi("/api/cart"),
    addItem: (productId, quantity = 1) =>
      fetchApi("/api/cart/add", {
        method: "POST",
        body: JSON.stringify({ product_id: productId, quantity }),
      }),
    removeItem: (productId) =>
      fetchApi(`/api/cart/remove/${productId}`, {
        method: "DELETE",
      }),
    clear: () =>
      fetchApi("/api/cart", {
        method: "DELETE",
      }),
  },

  // Wishlist endpoints
  wishlist: {
    getAll: () => fetchApi("/api/wishlist"),
    addItem: (productId) =>
      fetchApi("/api/wishlist/add", {
        method: "POST",
        body: JSON.stringify({ product_id: productId }),
      }),
    removeItem: (productId) =>
      fetchApi(`/api/wishlist/remove/${productId}`, {
        method: "DELETE",
      }),
  },

  // Orders endpoints
  orders: {
    getAll: () => fetchApi("/api/orders"),
    getById: (id) => fetchApi(`/api/orders/${id}`),
    create: (orderData) =>
      fetchApi("/api/orders", {
        method: "POST",
        body: JSON.stringify(orderData),
      }),
  },

  // Vendor endpoints (for vendor users)
  vendor: {
    getProducts: () => fetchApi("/api/products/vendor"),
    createProduct: (productData) =>
      fetchApi("/api/products", {
        method: "POST",
        body:
          productData instanceof FormData
            ? productData
            : JSON.stringify(productData),
      }),
    updateProduct: (productId, productData) =>
      fetchApi(`/api/products/${productId}`, {
        method: "PUT",
        body:
          productData instanceof FormData
            ? productData
            : JSON.stringify(productData),
      }),
    deleteProduct: (productId) =>
      fetchApi(`/api/products/${productId}`, {
        method: "DELETE",
      }),
    getOrders: (status) => {
      const queryParams = status ? `?status=${status}` : "";
      return fetchApi(`/api/vendor/orders${queryParams}`);
    },
    getOrderDetails: (orderId) => fetchApi(`/api/vendor/orders/${orderId}`),
    updateOrderStatus: (orderId, status) =>
      fetchApi(`/api/vendor/orders/${orderId}/status`, {
        method: "PATCH",
        body: JSON.stringify({ status }),
      }),
  },

  // User profile endpoints (now using auth endpoints)
  user: {
    getProfile: () => fetchApi("/api/auth/me"),
    updateProfile: (userData) =>
      fetchApi("/api/auth/profile", {
        method: "PUT",
        body: JSON.stringify(userData),
      }),
  },

  // Admin endpoints (for admin users)
  admin: {
    // Dashboard
    getStats: () => fetchApi("/api/admin/stats"),

    // User management
    getUsers: (params) => {
      const queryParams = new URLSearchParams(params).toString();
      return fetchApi(
        `/api/admin/users${queryParams ? `?${queryParams}` : ""}`
      );
    },
    updateUserStatus: (userId, isActive) =>
      fetchApi(`/api/admin/users/${userId}/status`, {
        method: "PATCH",
        body: JSON.stringify({ is_active: isActive }),
      }),

    // Product management
    getProducts: (params) => {
      const queryParams = new URLSearchParams(params).toString();
      return fetchApi(
        `/api/admin/products${queryParams ? `?${queryParams}` : ""}`
      );
    },

    // Order management
    getOrders: (params) => {
      const queryParams = new URLSearchParams(params).toString();
      return fetchApi(
        `/api/admin/orders${queryParams ? `?${queryParams}` : ""}`
      );
    },

    // Category management
    getCategories: () => fetchApi("/api/admin/categories"),
    createCategory: (categoryData) =>
      fetchApi("/api/admin/categories", {
        method: "POST",
        body: JSON.stringify(categoryData),
      }),
    updateCategory: (categoryId, categoryData) =>
      fetchApi(`/api/admin/categories/${categoryId}`, {
        method: "PUT",
        body: JSON.stringify(categoryData),
      }),
    deleteCategory: (categoryId) =>
      fetchApi(`/api/admin/categories/${categoryId}`, {
        method: "DELETE",
      }),
  },
};
