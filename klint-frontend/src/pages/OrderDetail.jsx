import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import api from '../services/api';

function OrderDetail() {
  const { id } = useParams();
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchOrderDetails = async () => {
      if (!isAuthenticated || !id) return;
      
      setLoading(true);
      try {
        const response = await api.orders.getById(id);
        if (response.success && response.order) {
          setOrder(response.order);
        } else {
          setError('Failed to fetch order details');
        }
      } catch (error) {
        console.error('Error fetching order details:', error);
        setError('An error occurred while fetching order details');
      } finally {
        setLoading(false);
      }
    };

    fetchOrderDetails();
  }, [isAuthenticated, id]);

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  if (loading) {
    return (
      <div className="container-custom py-12">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-klintblue border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-lg">Loading order details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container-custom py-12">
        <div className="bg-white rounded-lg shadow-sm p-8 text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <div className="flex justify-center gap-4 mt-6">
            <button 
              onClick={() => window.location.reload()}
              className="bg-klintblue text-white px-4 py-2 rounded-md hover:bg-klintAccent transition-colors"
            >
              Try Again
            </button>
            <button 
              onClick={() => navigate('/orders')}
              className="bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors"
            >
              Back to Orders
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="container-custom py-12">
        <div className="bg-white rounded-lg shadow-sm p-8 text-center">
          <p className="text-lg mb-4">Order not found or you don't have permission to view it.</p>
          <Link 
            to="/orders" 
            className="bg-klintblue text-white px-4 py-2 rounded-md hover:bg-klintAccent transition-colors inline-block"
          >
            Back to Orders
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container-custom py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Order #{order.id}</h1>
        <Link to="/orders" className="text-klintblue hover:text-klintAccent">
          Back to Orders
        </Link>
      </div>

      {/* Order Summary */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div>
            <p className="text-gray-500 text-sm mb-1">Order Date</p>
            <p className="font-medium">{new Date(order.created_at).toLocaleDateString()}</p>
          </div>
          
          <div>
            <p className="text-gray-500 text-sm mb-1">Status</p>
            <p>
              <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(order.status)}`}>
                {order.status}
              </span>
            </p>
          </div>
          
          <div>
            <p className="text-gray-500 text-sm mb-1">Payment Method</p>
            <p className="font-medium">{order.payment_method || 'Not specified'}</p>
          </div>
          
          <div>
            <p className="text-gray-500 text-sm mb-1">Total Amount</p>
            <p className="font-medium text-klintblue">${parseFloat(order.total_amount).toFixed(2)}</p>
          </div>
        </div>
        
        <div className="mt-6 pt-6 border-t border-gray-200">
          <h3 className="text-lg font-semibold mb-2">Shipping Address</h3>
          <p className="text-gray-700">{order.shipping_address || 'No shipping address provided'}</p>
        </div>
      </div>

      {/* Order Items */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-8">
        <h2 className="text-xl font-semibold p-6 border-b border-gray-200">Order Items</h2>
        
        {order.items && order.items.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Product
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Quantity
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Subtotal
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {order.items.map((item) => (
                  <tr key={item.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0 bg-gray-200 rounded-md overflow-hidden">
                          {item.product_image ? (
                            <img 
                              src={item.product_image} 
                              alt={item.product_name} 
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <div className="h-full w-full flex items-center justify-center text-gray-500 text-xs">
                              No image
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <Link 
                            to={`/products/${item.product_id}`}
                            className="text-sm font-medium text-gray-900 hover:text-klintblue"
                          >
                            {item.product_name}
                          </Link>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      ${parseFloat(item.price).toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.quantity}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      ${(parseFloat(item.price) * item.quantity).toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot className="bg-gray-50">
                <tr>
                  <td colSpan="3" className="px-6 py-4 text-right text-sm font-medium">
                    Subtotal:
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    ${parseFloat(order.subtotal || order.total_amount).toFixed(2)}
                  </td>
                </tr>
                {order.shipping_cost && (
                  <tr>
                    <td colSpan="3" className="px-6 py-4 text-right text-sm font-medium">
                      Shipping:
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      ${parseFloat(order.shipping_cost).toFixed(2)}
                    </td>
                  </tr>
                )}
                {order.tax && (
                  <tr>
                    <td colSpan="3" className="px-6 py-4 text-right text-sm font-medium">
                      Tax:
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      ${parseFloat(order.tax).toFixed(2)}
                    </td>
                  </tr>
                )}
                <tr className="bg-gray-100">
                  <td colSpan="3" className="px-6 py-4 text-right text-sm font-bold">
                    Total:
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-klintblue">
                    ${parseFloat(order.total_amount).toFixed(2)}
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        ) : (
          <div className="p-6 text-center text-gray-500">
            No items found for this order.
          </div>
        )}
      </div>

      {/* Order Actions */}
      {order.status === 'processing' && (
        <div className="flex justify-end">
          <button className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors">
            Cancel Order
          </button>
        </div>
      )}
    </div>
  );
}

export default OrderDetail;
