-- Use the existing database
USE geodhisc_klint_ecommerce;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('client', 'vendor', 'supplier', 'admin', 'staff') NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(50),
    last_name VA<PERSON>HA<PERSON>(50),
    phone VARCHAR(20),
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    vendor_id INT NOT NULL,
    category_id INT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    stock_quantity INT NOT NULL DEFAULT 0,
    sku VARCHAR(100) UNIQUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (vendor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- Product attributes table
CREATE TABLE IF NOT EXISTS product_attributes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    name VARCHAR(50) NOT NULL,
    value VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- Product images table
CREATE TABLE IF NOT EXISTS product_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    image_url VARCHAR(255) NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- Cart table
CREATE TABLE IF NOT EXISTS carts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Cart items table
CREATE TABLE IF NOT EXISTS cart_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    cart_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (cart_id) REFERENCES carts(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    UNIQUE KEY unique_cart_product (cart_id, product_id)
);

-- Wishlist table
CREATE TABLE IF NOT EXISTS wishlists (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_wishlist (user_id)
);

-- Wishlist items table
CREATE TABLE IF NOT EXISTS wishlist_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    wishlist_id INT NOT NULL,
    product_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (wishlist_id) REFERENCES wishlists(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    UNIQUE KEY unique_wishlist_product (wishlist_id, product_id)
);

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled') NOT NULL DEFAULT 'pending',
    total_amount DECIMAL(10, 2) NOT NULL,
    shipping_address TEXT NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Order items table
CREATE TABLE IF NOT EXISTS order_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    price_at_time DECIMAL(10, 2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- Insert sample data

-- Insert users
INSERT INTO users (username, email, password, role, first_name, last_name, phone, address) VALUES
('admin', '<EMAIL>', '$2b$10$X5z4v5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q', 'admin', 'Admin', 'User', '1234567890', '123 Admin St'),
('vendor1', '<EMAIL>', '$2b$10$X5z4v5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q', 'vendor', 'John', 'Doe', '1234567891', '456 Vendor Ave'),
('vendor2', '<EMAIL>', '$2b$10$X5z4v5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q', 'vendor', 'Jane', 'Smith', '1234567892', '789 Business Rd'),
('client1', '<EMAIL>', '$2b$10$X5z4v5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q5q', 'client', 'Bob', 'Johnson', '1234567893', '321 Customer Ln');

-- Insert categories
INSERT INTO categories (name, description, parent_id) VALUES
('Electronics', 'Electronic devices and accessories', NULL),
('Clothing', 'Apparel and fashion items', NULL),
('Home & Kitchen', 'Home appliances and kitchenware', NULL);

-- Insert products
INSERT INTO products (vendor_id, category_id, name, description, price, stock_quantity, sku, is_active) VALUES
(2, 1, 'iPhone 13 Pro', 'Latest iPhone with advanced camera system', 999.99, 50, 'IPH13PRO', TRUE),
(2, 1, 'Samsung Galaxy S21', 'Powerful Android smartphone', 799.99, 30, 'SGS21', TRUE),
(3, 1, 'MacBook Pro M1', 'Apple laptop with M1 chip', 1299.99, 20, 'MBPM1', TRUE),
(3, 1, 'Dell XPS 13', 'Premium Windows laptop', 1099.99, 15, 'DXP13', TRUE),
(2, 2, 'Men\'s Casual Shirt', 'Comfortable cotton shirt', 29.99, 100, 'MCS001', TRUE),
(2, 2, 'Women\'s Summer Dress', 'Light and stylish dress', 39.99, 80, 'WSD001', TRUE),
(3, 3, 'Smart Blender', 'High-performance kitchen blender', 89.99, 40, 'SB001', TRUE),
(3, 3, 'Air Fryer', 'Healthy cooking appliance', 79.99, 60, 'AF001', TRUE);

-- Insert product attributes
INSERT INTO product_attributes (product_id, name, value) VALUES
(1, 'Color', 'Graphite'),
(1, 'Storage', '256GB'),
(1, 'Screen Size', '6.1 inches'),
(2, 'Color', 'Phantom Black'),
(2, 'Storage', '128GB'),
(2, 'Screen Size', '6.2 inches'),
(3, 'Processor', 'Apple M1'),
(3, 'RAM', '16GB'),
(3, 'Storage', '512GB'),
(4, 'Processor', 'Intel i7'),
(4, 'RAM', '16GB'),
(4, 'Storage', '512GB');

-- Insert product images
INSERT INTO product_images (product_id, image_url, is_primary) VALUES
(1, 'https://example.com/iphone13pro1.jpg', TRUE),
(1, 'https://example.com/iphone13pro2.jpg', FALSE),
(2, 'https://example.com/s21-1.jpg', TRUE),
(2, 'https://example.com/s21-2.jpg', FALSE),
(3, 'https://example.com/mbp1.jpg', TRUE),
(3, 'https://example.com/mbp2.jpg', FALSE),
(4, 'https://example.com/dell1.jpg', TRUE),
(4, 'https://example.com/dell2.jpg', FALSE);

-- Insert cart for client1
INSERT INTO carts (user_id) VALUES (4);

-- Insert cart items
INSERT INTO cart_items (cart_id, product_id, quantity) VALUES
(1, 1, 1),
(1, 3, 1);

-- Insert wishlist for client1
INSERT INTO wishlists (user_id) VALUES (4);

-- Insert wishlist items
INSERT INTO wishlist_items (wishlist_id, product_id) VALUES
(1, 2),
(1, 4);

-- Insert sample order
INSERT INTO orders (user_id, status, total_amount, shipping_address, payment_method, payment_status) VALUES
(4, 'pending', 2299.98, '321 Customer Ln', 'credit_card', 'pending');

-- Insert order items
INSERT INTO order_items (order_id, product_id, quantity, price_at_time) VALUES
(1, 1, 1, 999.99),
(1, 3, 1, 1299.99); 