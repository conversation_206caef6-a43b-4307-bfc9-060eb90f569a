const express = require("express");
const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");
const {
  addToWishlist,
  removeFromWishlist,
  getWishlist,
} = require("../queries");

// Get wishlist contents
router.get("/", authMiddleware(["client"]), async (req, res) => {
  try {
    const wishlist = await getWishlist(req.user.userId);
    res.json({
      success: true,
      wishlist,
    });
  } catch (error) {
    console.error("Error getting wishlist:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving wishlist",
      error: error.message,
    });
  }
});

// Add item to wishlist
router.post("/add", authMiddleware(["client"]), async (req, res) => {
  try {
    const { product_id } = req.body;

    if (!product_id) {
      return res.status(400).json({
        success: false,
        message: "Product ID is required",
      });
    }

    const wishlist = await addToWishlist(req.user.userId, product_id);

    res.json({
      success: true,
      message: "Item added to wishlist successfully",
      wishlist,
    });
  } catch (error) {
    console.error("Error adding item to wishlist:", error);

    if (error.message === "Product not found or not available") {
      return res.status(404).json({
        success: false,
        message: error.message,
      });
    }

    if (error.message === "Product is already in wishlist") {
      return res.status(409).json({
        success: false,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      message: "Error adding item to wishlist",
      error: error.message,
    });
  }
});

// Remove item from wishlist
router.delete(
  "/remove/:productId",
  authMiddleware(["client"]),
  async (req, res) => {
    try {
      const productId = parseInt(req.params.productId);

      if (isNaN(productId)) {
        return res.status(400).json({
          success: false,
          message: "Invalid product ID",
        });
      }

      const wishlist = await removeFromWishlist(req.user.userId, productId);

      res.json({
        success: true,
        message: "Item removed from wishlist successfully",
        wishlist,
      });
    } catch (error) {
      console.error("Error removing item from wishlist:", error);

      if (error.message === "Wishlist not found") {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message === "Product not found in wishlist") {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: "Error removing item from wishlist",
        error: error.message,
      });
    }
  }
);

module.exports = router;
