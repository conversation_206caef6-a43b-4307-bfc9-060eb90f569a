import React, { useState, useEffect } from "react";
import { Link, useParams, useNavigate } from "react-router-dom";
import api from "../../services/api";
import VendorNav from "../../components/vendor/VendorNav";

function VendorOrderDetail() {
  const { id } = useParams();
  const navigate = useNavigate();

  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [updatingStatus, setUpdatingStatus] = useState(false);

  useEffect(() => {
    fetchOrderDetails();
  }, [id]);

  const fetchOrderDetails = async () => {
    setLoading(true);
    try {
      const response = await api.vendor.getOrderDetails(id);

      if (response.success && response.order) {
        setOrder(response.order);
      } else {
        setError("Failed to fetch order details");
      }
    } catch (error) {
      console.error("Error fetching order details:", error);
      setError("An error occurred while fetching order details");
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateStatus = async (newStatus) => {
    setUpdatingStatus(true);
    try {
      const response = await api.vendor.updateOrderStatus(id, newStatus);

      if (response.success) {
        // Update the order status in the state
        setOrder({
          ...order,
          status: newStatus,
        });
      } else {
        setError("Failed to update order status");
      }
    } catch (error) {
      console.error("Error updating order status:", error);
      setError("An error occurred while updating order status");
    } finally {
      setUpdatingStatus(false);
    }
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case "completed":
      case "delivered":
        return "bg-green-100 text-green-800";
      case "processing":
        return "bg-blue-100 text-blue-800";
      case "shipped":
        return "bg-purple-100 text-purple-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-yellow-100 text-yellow-800";
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="w-16 h-16 border-4 border-klintblue border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-6 rounded-lg text-center">
        <p className="text-red-600 mb-4">{error}</p>
        <div className="flex justify-center space-x-4">
          <button
            onClick={() => fetchOrderDetails()}
            className="bg-klintblue text-white px-4 py-2 rounded-md hover:bg-klintAccent transition-colors"
          >
            Try Again
          </button>
          <button
            onClick={() => navigate("/vendor/orders")}
            className="bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors"
          >
            Back to Orders
          </button>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-8 text-center">
        <p className="text-lg mb-4">
          Order not found or you don't have permission to view it.
        </p>
        <Link
          to="/vendor/orders"
          className="bg-klintblue text-white px-4 py-2 rounded-md hover:bg-klintAccent transition-colors inline-block"
        >
          Back to Orders
        </Link>
      </div>
    );
  }

  return (
    <div className="container-custom py-4">
      <VendorNav />

      <div className="flex justify-between items-center mb-3">
        <h1 className="text-base font-bold">Order #{order.id}</h1>
        <Link
          to="/vendor/orders"
          className="text-klintblue hover:underline text-xs"
        >
          Back to Orders
        </Link>
      </div>

      {/* Order Summary - Compact */}
      <div className="bg-white rounded shadow-sm p-3 mb-3">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          <div>
            <p className="text-gray-500 text-xs mb-0.5">Order Date</p>
            <p className="font-medium text-xs">
              {new Date(order.created_at).toLocaleDateString()}
            </p>
          </div>

          <div>
            <p className="text-gray-500 text-xs mb-0.5">Status</p>
            <p>
              <span
                className={`px-1.5 py-0.5 inline-flex text-xs leading-4 font-medium rounded-full ${getStatusBadgeClass(
                  order.status
                )}`}
              >
                {order.status}
              </span>
            </p>
          </div>

          <div>
            <p className="text-gray-500 text-xs mb-0.5">Payment Method</p>
            <p className="font-medium text-xs">
              {order.payment_method || "Not specified"}
            </p>
          </div>

          <div>
            <p className="text-gray-500 text-xs mb-0.5">Total Amount</p>
            <p className="font-medium text-klintblue text-xs">
              ${parseFloat(order.total_amount).toFixed(2)}
            </p>
          </div>
        </div>

        <div className="mt-3 pt-3 border-t border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <h3 className="text-sm font-semibold mb-1">
                Customer Information
              </h3>
              <p className="text-gray-700 text-xs">
                <span className="font-medium">Name:</span>{" "}
                {order.customer_name || "Customer #" + order.user_id}
              </p>
              <p className="text-gray-700 text-xs">
                <span className="font-medium">Email:</span>{" "}
                {order.customer_email || "Not available"}
              </p>
              <p className="text-gray-700 text-xs">
                <span className="font-medium">Phone:</span>{" "}
                {order.customer_phone || "Not available"}
              </p>
            </div>

            <div>
              <h3 className="text-sm font-semibold mb-1">Shipping Address</h3>
              <p className="text-gray-700 text-xs">
                {order.shipping_address || "No shipping address provided"}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Order Items - Compact */}
      <div className="bg-white rounded shadow-sm overflow-hidden mb-3">
        <h2 className="text-sm font-semibold p-3 border-b border-gray-200">
          Order Items
        </h2>

        {order.items && order.items.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Product
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Price
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Qty
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Subtotal
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {order.items.map((item) => (
                  <tr key={item.id} className="hover:bg-gray-50">
                    <td className="px-3 py-2">
                      <div className="flex items-center">
                        <div className="h-8 w-8 flex-shrink-0 bg-gray-200 rounded overflow-hidden">
                          {item.product_image ? (
                            <img
                              src={item.product_image}
                              alt={item.product_name}
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <div className="h-full w-full flex items-center justify-center text-gray-500 text-xs">
                              No img
                            </div>
                          )}
                        </div>
                        <div className="ml-2">
                          <Link
                            to={`/vendor/products/edit/${item.product_id}`}
                            className="text-xs font-medium text-gray-900 hover:text-klintblue"
                          >
                            {item.product_name}
                          </Link>
                          <p className="text-xs text-gray-500">
                            SKU: {item.sku || "N/A"}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500">
                      ${parseFloat(item.price_at_time).toFixed(2)}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500">
                      {item.quantity}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-xs font-medium text-gray-900">
                      $
                      {(parseFloat(item.price_at_time) * item.quantity).toFixed(
                        2
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot className="bg-gray-50">
                <tr>
                  <td
                    colSpan="3"
                    className="px-3 py-2 text-right text-xs font-medium"
                  >
                    Subtotal:
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-xs font-medium">
                    $
                    {parseFloat(order.subtotal || order.total_amount).toFixed(
                      2
                    )}
                  </td>
                </tr>
                {order.shipping_cost && (
                  <tr>
                    <td
                      colSpan="3"
                      className="px-3 py-2 text-right text-xs font-medium"
                    >
                      Shipping:
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-xs font-medium">
                      ${parseFloat(order.shipping_cost).toFixed(2)}
                    </td>
                  </tr>
                )}
                {order.tax && (
                  <tr>
                    <td
                      colSpan="3"
                      className="px-3 py-2 text-right text-xs font-medium"
                    >
                      Tax:
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap text-xs font-medium">
                      ${parseFloat(order.tax).toFixed(2)}
                    </td>
                  </tr>
                )}
                <tr className="bg-gray-100">
                  <td
                    colSpan="3"
                    className="px-3 py-2 text-right text-xs font-bold"
                  >
                    Total:
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-xs font-bold text-klintblue">
                    ${parseFloat(order.total_amount).toFixed(2)}
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        ) : (
          <div className="p-3 text-center text-xs text-gray-500">
            No items found for this order.
          </div>
        )}
      </div>

      {/* Order Actions - Compact */}
      <div className="bg-white rounded shadow-sm p-3">
        <h2 className="text-sm font-semibold mb-2">Order Actions</h2>
        <div className="flex flex-wrap gap-2">
          {order.status === "pending" && (
            <button
              onClick={() => handleUpdateStatus("processing")}
              disabled={updatingStatus}
              className="bg-blue-600 text-white px-3 py-1 text-xs rounded hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {updatingStatus ? "Updating..." : "Process Order"}
            </button>
          )}

          {order.status === "processing" && (
            <button
              onClick={() => handleUpdateStatus("shipped")}
              disabled={updatingStatus}
              className="bg-purple-600 text-white px-3 py-1 text-xs rounded hover:bg-purple-700 transition-colors disabled:opacity-50"
            >
              {updatingStatus ? "Updating..." : "Mark as Shipped"}
            </button>
          )}

          {order.status === "shipped" && (
            <button
              onClick={() => handleUpdateStatus("delivered")}
              disabled={updatingStatus}
              className="bg-green-600 text-white px-3 py-1 text-xs rounded hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              {updatingStatus ? "Updating..." : "Mark as Delivered"}
            </button>
          )}

          {(order.status === "pending" || order.status === "processing") && (
            <button
              onClick={() => handleUpdateStatus("cancelled")}
              disabled={updatingStatus}
              className="bg-red-600 text-white px-3 py-1 text-xs rounded hover:bg-red-700 transition-colors disabled:opacity-50"
            >
              {updatingStatus ? "Updating..." : "Cancel Order"}
            </button>
          )}

          <Link
            to={`/vendor/orders`}
            className="bg-gray-200 text-gray-800 px-3 py-1 text-xs rounded hover:bg-gray-300 transition-colors"
          >
            Back to Orders
          </Link>
        </div>
      </div>
    </div>
  );
}

export default VendorOrderDetail;
