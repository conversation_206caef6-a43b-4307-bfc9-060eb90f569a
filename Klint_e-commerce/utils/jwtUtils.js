const jwt = require("jsonwebtoken");
require("dotenv").config();

// Token configuration
const TOKEN_CONFIG = {
  expiresIn: process.env.JWT_EXPIRY || "1h",
  algorithm: "HS256",
  issuer: process.env.JWT_ISSUER || "klint-api",
};

// Custom error for token-related issues
class TokenError extends Error {
  constructor(message, type = "TokenError") {
    super(message);
    this.name = type;
  }
}

/**
 * Generates a JWT token for a user
 * @param {number} userId - The user's ID
 * @param {string} role - The user's role
 * @param {string} email - The user's email
 * @returns {string} The generated JWT token
 * @throws {TokenError} If token generation fails
 */
const generateToken = (userId, role, email) => {
  try {
    if (!userId || !role || !email) {
      throw new TokenError(
        "Missing required parameters for token generation",
        "ValidationError"
      );
    }

    if (!process.env.JWT_SECRET) {
      throw new TokenError(
        "JWT secret is not configured",
        "ConfigurationError"
      );
    }

    const payload = {
      userId,
      role,
      email,
      timestamp: Date.now(),
    };

    const token = jwt.sign(payload, process.env.JWT_SECRET, {
      ...TOKEN_CONFIG,
      subject: userId.toString(),
    });

    return token;
  } catch (error) {
    console.error("Token generation error:", error);
    throw new TokenError("Failed to generate token", "GenerationError");
  }
};

/**
 * Verifies and decodes a JWT token
 * @param {string} token - The JWT token to verify
 * @returns {Object} The decoded token payload
 * @throws {TokenError} If token verification fails
 */
const verifyToken = (token) => {
  try {
    if (!token) {
      throw new TokenError("No token provided", "ValidationError");
    }

    if (!process.env.JWT_SECRET) {
      throw new TokenError(
        "JWT secret is not configured",
        "ConfigurationError"
      );
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET, {
      algorithms: [TOKEN_CONFIG.algorithm],
      issuer: TOKEN_CONFIG.issuer,
    });

    // Additional validation of token payload
    if (!decoded.userId || !decoded.role) {
      throw new TokenError("Invalid token payload", "ValidationError");
    }

    // Map the decoded payload to match the expected format
    return {
      userId: decoded.userId,
      role: decoded.role,
      email: decoded.email,
      timestamp: decoded.timestamp,
    };
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new TokenError("Token has expired", "TokenExpiredError");
    }
    if (error instanceof jwt.JsonWebTokenError) {
      throw new TokenError("Invalid token", "TokenValidationError");
    }

    console.error("Token verification error:", {
      error: error.message,
      tokenPresent: !!token,
    });

    throw new TokenError(
      error.message || "Token verification failed",
      error.name || "TokenVerificationError"
    );
  }
};

module.exports = {
  generateToken,
  verifyToken,
  TokenError,
};
