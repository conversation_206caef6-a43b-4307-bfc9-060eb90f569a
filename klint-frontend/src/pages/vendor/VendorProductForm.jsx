import React, { useState, useEffect } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import api from "../../services/api";
import VendorNav from "../../components/vendor/VendorNav";

function VendorProductForm() {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditMode = !!id;

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    price: "",
    stock_quantity: "",
    sku: "",
    category_id: "",
    is_active: true,
    images: [],
    attributes: [],
  });

  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [formErrors, setFormErrors] = useState({});
  const [newAttribute, setNewAttribute] = useState({ name: "", value: "" });
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [uploadProgress, setUploadProgress] = useState({});

  // Fetch categories and product data (if in edit mode)
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch categories
        const categoriesResponse = await api.categories.getAll();
        if (categoriesResponse.success) {
          setCategories(categoriesResponse.categories || []);
        }

        // If in edit mode, fetch product data
        if (isEditMode) {
          const productResponse = await api.products.getById(id);
          if (productResponse.success && productResponse.product) {
            const product = productResponse.product;

            // Process images to ensure they have the correct format
            let processedImages = [];
            if (Array.isArray(product.images)) {
              processedImages = product.images.map((img) => {
                // If the image is already in the correct format, use it
                if (typeof img === "object" && img.url) {
                  return img;
                }
                // If it's a string, convert it to the expected object format
                return { url: img, is_primary: false };
              });

              // Ensure at least one image is marked as primary
              if (
                processedImages.length > 0 &&
                !processedImages.some((img) => img.is_primary)
              ) {
                processedImages[0].is_primary = true;
              }
            }

            // Format the product data for the form
            setFormData({
              name: product.name || "",
              description: product.description || "",
              price: product.price || "",
              stock_quantity: product.stock_quantity || "",
              sku: product.sku || "",
              category_id: product.category_id || "",
              is_active:
                product.is_active !== undefined ? product.is_active : true,
              images: processedImages,
              attributes: Array.isArray(product.attributes)
                ? product.attributes
                : [],
            });
          } else {
            setError("Failed to fetch product data");
          }
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("An error occurred while fetching data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id, isEditMode]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  const handleAttributeChange = (e) => {
    const { name, value } = e.target;
    setNewAttribute({
      ...newAttribute,
      [name]: value,
    });
  };

  const addAttribute = () => {
    if (newAttribute.name.trim() && newAttribute.value.trim()) {
      setFormData({
        ...formData,
        attributes: [...formData.attributes, { ...newAttribute }],
      });
      setNewAttribute({ name: "", value: "" });
    }
  };

  const removeAttribute = (index) => {
    const updatedAttributes = [...formData.attributes];
    updatedAttributes.splice(index, 1);
    setFormData({
      ...formData,
      attributes: updatedAttributes,
    });
  };

  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    setSelectedFiles(files);
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.name.trim()) {
      errors.name = "Product name is required";
    }

    if (!formData.price) {
      errors.price = "Price is required";
    } else if (isNaN(formData.price) || parseFloat(formData.price) <= 0) {
      errors.price = "Price must be a positive number";
    }

    if (!formData.stock_quantity) {
      errors.stock_quantity = "Stock quantity is required";
    } else if (
      isNaN(formData.stock_quantity) ||
      parseInt(formData.stock_quantity) < 0
    ) {
      errors.stock_quantity = "Stock quantity must be a non-negative number";
    }

    if (!formData.sku.trim()) {
      errors.sku = "SKU is required";
    }

    if (formData.images.length === 0) {
      errors.images = "At least one product image is required";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setSubmitting(true);
    setError(null);

    try {
      // Create FormData object for multipart/form-data
      const formDataToSend = new FormData();

      // Add basic product data
      formDataToSend.append("name", formData.name);
      formDataToSend.append("description", formData.description);
      formDataToSend.append("price", formData.price);
      formDataToSend.append("stock_quantity", formData.stock_quantity);
      formDataToSend.append("sku", formData.sku);
      formDataToSend.append("category_id", formData.category_id || "");

      // Add attributes as JSON string
      if (formData.attributes.length > 0) {
        formDataToSend.append(
          "attributes",
          JSON.stringify(formData.attributes)
        );
      }

      // Add existing images if in edit mode
      if (isEditMode && formData.images.length > 0) {
        formDataToSend.append(
          "existing_images",
          JSON.stringify(formData.images)
        );
      }

      // Add new image files
      selectedFiles.forEach((file) => {
        formDataToSend.append("images", file);
      });

      let response;
      if (isEditMode) {
        response = await api.vendor.updateProduct(id, formDataToSend);
      } else {
        response = await api.vendor.createProduct(formDataToSend);
      }

      if (response.success) {
        navigate("/vendor/products");
      } else {
        setError(response.message || "Failed to save product");
      }
    } catch (error) {
      console.error("Error saving product:", error);
      setError("An error occurred while saving the product");
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="w-16 h-16 border-4 border-klintblue border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <VendorNav />
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold mb-6">
            {isEditMode ? "Edit Product" : "Add New Product"}
          </h1>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Product Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 ${
                    formErrors.name ? "border-red-500" : ""
                  }`}
                />
                {formErrors.name && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.name}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  SKU *
                </label>
                <input
                  type="text"
                  name="sku"
                  value={formData.sku}
                  onChange={handleChange}
                  className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 ${
                    formErrors.sku ? "border-red-500" : ""
                  }`}
                />
                {formErrors.sku && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.sku}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows="4"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Price *
                </label>
                <input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                  className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 ${
                    formErrors.price ? "border-red-500" : ""
                  }`}
                />
                {formErrors.price && (
                  <p className="mt-1 text-sm text-red-600">
                    {formErrors.price}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Stock Quantity *
                </label>
                <input
                  type="number"
                  name="stock_quantity"
                  value={formData.stock_quantity}
                  onChange={handleChange}
                  min="0"
                  className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 ${
                    formErrors.stock_quantity ? "border-red-500" : ""
                  }`}
                />
                {formErrors.stock_quantity && (
                  <p className="mt-1 text-sm text-red-600">
                    {formErrors.stock_quantity}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Category
              </label>
              <select
                name="category_id"
                value={formData.category_id}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              >
                <option value="">Select a category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Image Upload Section */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Images *
              </label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                <div className="space-y-1 text-center">
                  <svg
                    className="mx-auto h-12 w-12 text-gray-400"
                    stroke="currentColor"
                    fill="none"
                    viewBox="0 0 48 48"
                    aria-hidden="true"
                  >
                    <path
                      d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                      strokeWidth={2}
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <div className="flex text-sm text-gray-600">
                    <label
                      htmlFor="file-upload"
                      className="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
                    >
                      <span>Upload files</span>
                      <input
                        id="file-upload"
                        name="file-upload"
                        type="file"
                        multiple
                        accept="image/*"
                        onChange={handleFileSelect}
                        className="sr-only"
                      />
                    </label>
                    <p className="pl-1">or drag and drop</p>
                  </div>
                  <p className="text-xs text-gray-500">
                    PNG, JPG, GIF up to 5MB each
                  </p>
                </div>
              </div>
              {formErrors.images && (
                <p className="mt-1 text-sm text-red-600">{formErrors.images}</p>
              )}

              {/* Selected Files Preview */}
              {selectedFiles.length > 0 && (
                <div className="mt-4">
                  <h3 className="text-sm font-medium text-gray-700">
                    Selected Files:
                  </h3>
                  <ul className="mt-2 grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4">
                    {selectedFiles.map((file, index) => (
                      <li key={index} className="relative">
                        <div className="group block w-full aspect-w-10 aspect-h-7 rounded-lg bg-gray-100 overflow-hidden">
                          <img
                            src={URL.createObjectURL(file)}
                            alt={`Preview ${index + 1}`}
                            className="object-cover pointer-events-none"
                          />
                          <button
                            type="button"
                            onClick={() => {
                              setSelectedFiles(
                                selectedFiles.filter((_, i) => i !== index)
                              );
                            }}
                            className="absolute inset-0 focus:outline-none"
                          >
                            <span className="sr-only">Remove image</span>
                          </button>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Existing Images (in edit mode) */}
              {isEditMode && formData.images.length > 0 && (
                <div className="mt-4">
                  <h3 className="text-sm font-medium text-gray-700">
                    Existing Images:
                  </h3>
                  <ul className="mt-2 grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4">
                    {formData.images.map((image, index) => (
                      <li key={index} className="relative">
                        <div className="group block w-full aspect-w-10 aspect-h-7 rounded-lg bg-gray-100 overflow-hidden">
                          <img
                            src={image.url}
                            alt={`Product ${index + 1}`}
                            className="object-cover pointer-events-none"
                          />
                          <button
                            type="button"
                            onClick={() => removeImage(index)}
                            className="absolute inset-0 focus:outline-none"
                          >
                            <span className="sr-only">Remove image</span>
                          </button>
                          {image.is_primary && (
                            <div className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded text-xs">
                              Primary
                            </div>
                          )}
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Attributes Section */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Attributes
              </label>
              <div className="space-y-4">
                {formData.attributes.map((attr, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={attr.name}
                      readOnly
                      className="block w-1/3 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                    <input
                      type="text"
                      value={attr.value}
                      readOnly
                      className="block w-1/3 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                    <button
                      type="button"
                      onClick={() => removeAttribute(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      Remove
                    </button>
                  </div>
                ))}

                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    name="name"
                    value={newAttribute.name}
                    onChange={handleAttributeChange}
                    placeholder="Attribute name"
                    className="block w-1/3 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                  />
                  <input
                    type="text"
                    name="value"
                    value={newAttribute.value}
                    onChange={handleAttributeChange}
                    placeholder="Attribute value"
                    className="block w-1/3 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                  />
                  <button
                    type="button"
                    onClick={addAttribute}
                    className="text-indigo-600 hover:text-indigo-800"
                  >
                    Add
                  </button>
                </div>
              </div>
            </div>

            {/* Status Toggle */}
            <div className="flex items-center">
              <input
                type="checkbox"
                name="is_active"
                checked={formData.is_active}
                onChange={handleChange}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Product is active
              </label>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-4">
              <Link
                to="/vendor/products"
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={submitting}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {submitting
                  ? "Saving..."
                  : isEditMode
                  ? "Update Product"
                  : "Create Product"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default VendorProductForm;
