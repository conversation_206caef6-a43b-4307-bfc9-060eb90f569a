import { useState, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import api from "../services/api";

function Products() {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [priceRange, setPriceRange] = useState(1000);
  // const [totalProducts, setTotalProducts] = useState(0); // Commented out as it's not currently used
  const [sortOption, setSortOption] = useState("featured");

  const location = useLocation();
  const navigate = useNavigate();

  // Get query parameters
  const queryParams = new URLSearchParams(location.search);
  const categoryParam = queryParams.get("category");
  const priceParam = queryParams.get("price");

  useEffect(() => {
    // If category is specified in URL, select it
    if (categoryParam) {
      // Check if it's a comma-separated list of categories
      if (categoryParam.includes(",")) {
        const categoryIds = categoryParam
          .split(",")
          .map((id) => parseInt(id))
          .filter((id) => !isNaN(id));

        console.log("Setting multiple categories from URL:", categoryIds);
        setSelectedCategories(categoryIds);
      } else {
        // Single category
        const categoryId = parseInt(categoryParam);
        if (!isNaN(categoryId)) {
          console.log("Setting single category from URL:", categoryId);
          setSelectedCategories([categoryId]);
        }
      }
    } else {
      // Clear selected categories if no category param
      console.log("Clearing categories (no URL param)");
      setSelectedCategories([]);
    }

    // Fetch categories
    const fetchCategories = async () => {
      try {
        const response = await api.categories.getAll();
        if (response.success && response.categories) {
          setCategories(response.categories);
        }
      } catch (err) {
        console.error("Error fetching categories:", err);
      }
    };

    fetchCategories();
  }, [categoryParam]);

  // Handle price parameter from URL
  useEffect(() => {
    if (priceParam) {
      const price = parseInt(priceParam);
      if (!isNaN(price) && price >= 0 && price <= 1000) {
        setPriceRange(price);
      }
    }
  }, [priceParam]);

  useEffect(() => {
    // Fetch products
    const fetchProducts = async () => {
      setLoading(true);
      try {
        let response;

        // Log for debugging
        console.log("Fetching products with categories:", selectedCategories);

        // Always use the getAll endpoint with category filter for consistency
        const params = {};

        // Add category filter if any categories are selected
        if (selectedCategories.length > 0) {
          params.category = selectedCategories.join(",");
          console.log("Using category filter:", params.category);
        }

        response = await api.products.getAll(params);

        // Log the response for debugging
        console.log("API response:", response);

        if (response.success && response.products) {
          // Start with all products from the response
          let filteredProducts = response.products;

          // Apply category filter on the client side if categories are selected
          if (selectedCategories.length > 0) {
            console.log(
              "Filtering products by categories:",
              selectedCategories
            );
            filteredProducts = filteredProducts.filter((product) =>
              selectedCategories.includes(parseInt(product.category_id))
            );
            console.log(
              "After category filtering:",
              filteredProducts.length,
              "products remain"
            );
          }

          // Apply price filter on the client side
          if (priceRange < 1000) {
            const beforeCount = filteredProducts.length;
            filteredProducts = filteredProducts.filter(
              (product) => parseFloat(product.price) <= priceRange
            );
            console.log(
              `Price filter (≤$${priceRange}): ${beforeCount} → ${filteredProducts.length} products`
            );
          }

          // Apply sorting
          const sortedProducts = [...filteredProducts].sort((a, b) => {
            switch (sortOption) {
              case "price-low-high":
                return parseFloat(a.price) - parseFloat(b.price);
              case "price-high-low":
                return parseFloat(b.price) - parseFloat(a.price);
              case "newest":
                return (
                  new Date(b.created_at || 0) - new Date(a.created_at || 0)
                );
              default:
                return 0; // featured - no specific sort
            }
          });

          setProducts(sortedProducts);
          // setTotalProducts(sortedProducts.length); // Removed as we're not using totalProducts
          setError(null);
        } else {
          throw new Error("Invalid response format");
        }
      } catch (err) {
        console.error("Error fetching products:", err);
        setError("Failed to load products. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [selectedCategories, priceRange, sortOption]);
  // Handle category checkbox change
  const handleCategoryChange = (categoryId) => {
    // Ensure categoryId is a number
    const numericCategoryId = parseInt(categoryId);

    setSelectedCategories((prev) => {
      let newCategories;
      if (prev.includes(numericCategoryId)) {
        newCategories = prev.filter((id) => id !== numericCategoryId);
      } else {
        newCategories = [...prev, numericCategoryId];
      }
      return newCategories;
    });

    // Log for debugging
    console.log(
      `Category ${categoryId} toggled. New selection:`,
      selectedCategories.includes(parseInt(categoryId))
        ? selectedCategories.filter((id) => id !== parseInt(categoryId))
        : [...selectedCategories, parseInt(categoryId)]
    );
  };

  // Update URL parameters when selectedCategories change
  useEffect(() => {
    // Don't update URL during initial load or when categories are set from URL
    if (loading) return;

    const params = new URLSearchParams(location.search);

    // Update or remove category parameter
    if (selectedCategories.length === 1) {
      params.set("category", selectedCategories[0]);
    } else if (selectedCategories.length > 1) {
      params.set("category", selectedCategories.join(","));
    } else {
      params.delete("category");
    }

    // Preserve price parameter if it exists
    if (priceParam && priceParam !== "1000") {
      params.set("price", priceParam);
    }

    // Update URL without reloading the page
    navigate(`${location.pathname}?${params.toString()}`, { replace: true });
  }, [selectedCategories, loading, location.pathname, navigate, priceParam]);

  // Update URL when price range changes
  useEffect(() => {
    // Don't update URL during initial load
    if (loading) return;

    // Use a debounce to avoid too many URL updates while sliding
    const timer = setTimeout(() => {
      const params = new URLSearchParams(location.search);

      // Update or remove price parameter
      if (priceRange < 1000) {
        params.set("price", priceRange);
      } else {
        params.delete("price");
      }

      // Update URL without reloading the page
      navigate(`${location.pathname}?${params.toString()}`, { replace: true });
    }, 500); // 500ms debounce

    return () => clearTimeout(timer);
  }, [priceRange, loading, location.pathname, location.search, navigate]);

  // Handle price range change
  const handlePriceRangeChange = (e) => {
    setPriceRange(parseInt(e.target.value));
  };

  // Handle sort change
  const handleSortChange = (e) => {
    setSortOption(e.target.value);
  };

  // Handle add to cart
  const handleAddToCart = async (productId) => {
    try {
      const response = await api.cart.addItem(productId, 1);
      if (response.success) {
        alert("Product added to cart successfully!");
      }
    } catch (err) {
      console.error("Error adding product to cart:", err);
      alert("Failed to add product to cart. Please try again.");
    }
  };

  // Show loading state
  if (loading && products.length === 0) {
    return (
      <div className="container-custom py-8">
        <h1 className="text-3xl font-bold mb-8">All Products</h1>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-klintblue border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-lg">Loading products...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container-custom py-8">
      <h1 className="text-3xl font-bold mb-8">All Products</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      <div className="flex flex-col md:flex-row gap-6">
        {/* Filters sidebar */}
        <div className="w-full md:w-64 mb-6 md:mb-0">
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <h2 className="font-bold text-lg mb-4">Filters</h2>

            <div className="mb-4">
              <h3 className="font-medium mb-2">Categories</h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <div key={category.id} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`cat-${category.id}`}
                      className="mr-2"
                      checked={selectedCategories.includes(
                        parseInt(category.id)
                      )}
                      onChange={() => handleCategoryChange(category.id)}
                    />
                    <label
                      htmlFor={`cat-${category.id}`}
                      className={
                        selectedCategories.includes(parseInt(category.id))
                          ? "font-medium text-klintblue"
                          : ""
                      }
                    >
                      {category.name}
                    </label>
                  </div>
                ))}
                {categories.length === 0 && (
                  <p className="text-sm text-gray-500">Loading categories...</p>
                )}
              </div>
            </div>

            <div className="mb-4">
              <h3 className="font-medium mb-2">Price Range</h3>
              <div className="flex items-center">
                <input
                  type="range"
                  min="0"
                  max="1000"
                  value={priceRange}
                  onChange={handlePriceRangeChange}
                  className="w-full"
                />
              </div>
              <div className="flex justify-between mt-2">
                <span>$0</span>
                <span>${priceRange}</span>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                className="btn-secondary"
                onClick={() => {
                  // Log for debugging
                  console.log("Clearing all filters");

                  // Reset state
                  setSelectedCategories([]);
                  setPriceRange(1000);
                  setSortOption("featured");

                  // Force a reload of products
                  setLoading(true);

                  // Clear URL parameters in the next render cycle
                  setTimeout(() => {
                    // Clear URL parameters and navigate to clean URL
                    console.log("Clearing URL parameters");
                    navigate(location.pathname, { replace: true });
                  }, 0);
                }}
              >
                Clear All Filters
              </button>
            </div>
          </div>
        </div>

        {/* Products grid */}
        <div className="flex-1">
          <div className="flex justify-between items-center mb-6">
            <div>
              <p className="text-gray-600">
                {products.length > 0
                  ? `Showing ${products.length} products`
                  : "No products found"}
              </p>
              {selectedCategories.length > 0 && (
                <p className="text-xs text-gray-500 mt-1">
                  Filtered by categories:{" "}
                  {selectedCategories
                    .map((catId) => {
                      const category = categories.find(
                        (c) => parseInt(c.id) === catId
                      );
                      return category ? category.name : catId;
                    })
                    .join(", ")}
                </p>
              )}
            </div>
            <select
              className="border rounded-md p-2"
              value={sortOption}
              onChange={handleSortChange}
            >
              <option value="featured">Sort by: Featured</option>
              <option value="price-low-high">Price: Low to High</option>
              <option value="price-high-low">Price: High to Low</option>
              <option value="newest">Newest First</option>
            </select>
          </div>

          {products.length === 0 && !loading ? (
            <div className="bg-gray-100 p-8 rounded-lg text-center">
              <p className="text-lg text-gray-600">
                No products found matching your criteria.
              </p>
              <button
                onClick={() => setSelectedCategories([])}
                className="mt-4 btn-primary"
              >
                Clear Filters
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Product cards */}
              {products.map((product) => (
                <div
                  key={product.id}
                  className="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
                >
                  <Link to={`/products/${product.id}`} className="block">
                    <div className="h-48 bg-gray-200 flex items-center justify-center">
                      {product.images && product.images.length > 0 ? (
                        <img
                          src={
                            product.images.find((img) => img.is_primary)?.url ||
                            product.images[0].url
                          }
                          alt={product.name}
                          className="h-full w-full object-cover"
                          onError={(e) => {
                            console.log(
                              "Product list image failed to load:",
                              e.target.src
                            );
                            e.target.onerror = null;
                            e.target.src = "/placeholder.svg";
                          }}
                        />
                      ) : (
                        <span className="text-gray-500">No Image</span>
                      )}
                    </div>
                    <div className="p-4">
                      <h3 className="font-medium text-lg hover:text-klintblue transition-colors">
                        {product.name}
                      </h3>
                      <p className="text-gray-600 text-sm mb-2">
                        {product.category_name || "Uncategorized"}
                      </p>
                      <p className="font-bold text-klintblue">
                        ${parseFloat(product.price).toFixed(2)}
                      </p>
                    </div>
                  </Link>
                  <div className="px-4 pb-4">
                    <button
                      className="btn-primary w-full"
                      onClick={(e) => {
                        e.preventDefault();
                        handleAddToCart(product.id);
                      }}
                      disabled={product.stock_quantity <= 0}
                    >
                      {product.stock_quantity > 0
                        ? "Add to Cart"
                        : "Out of Stock"}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Products;
