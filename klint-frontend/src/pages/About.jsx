import React from 'react';
import { Link } from 'react-router-dom';

function About() {
  return (
    <div className="container-custom py-8">
      <h1 className="text-3xl font-bold mb-8">About Klint</h1>
      
      {/* Hero Section */}
      <div className="bg-klintblue text-white rounded-lg p-8 mb-12">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-2xl font-bold mb-4">Our Mission</h2>
          <p className="text-lg mb-0">
            At Klint, our mission is to provide high-quality products at affordable prices while delivering exceptional customer service. We strive to make online shopping convenient, enjoyable, and accessible to everyone.
          </p>
        </div>
      </div>
      
      {/* Our Story Section */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Our Story</h2>
        <div className="flex flex-col md:flex-row gap-8">
          <div className="md:w-1/2">
            <div className="h-64 bg-gray-200 rounded-lg flex items-center justify-center mb-4">
              <span className="text-gray-500">Company Image</span>
            </div>
          </div>
          <div className="md:w-1/2">
            <p className="mb-4">
              Klint was founded in 2020 with a simple idea: to create an online shopping experience that puts customers first. What started as a small operation has grown into a thriving e-commerce platform offering thousands of products across multiple categories.
            </p>
            <p className="mb-4">
              Our journey began when our founders recognized a gap in the market for an online store that combined quality products, competitive prices, and exceptional customer service. They set out to build a platform that would make shopping online as enjoyable and straightforward as possible.
            </p>
            <p>
              Today, Klint serves customers nationwide, offering a carefully curated selection of products that meet our strict quality standards. We continue to grow and evolve, always keeping our customers' needs at the heart of everything we do.
            </p>
          </div>
        </div>
      </div>
      
      {/* Our Values Section */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Our Values</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="h-12 w-12 bg-klintblue rounded-full flex items-center justify-center text-white mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold mb-2">Quality</h3>
            <p>We are committed to offering only the highest quality products. Each item in our inventory is carefully selected and rigorously tested to ensure it meets our standards.</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="h-12 w-12 bg-klintblue rounded-full flex items-center justify-center text-white mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold mb-2">Customer Service</h3>
            <p>Our customers are at the heart of everything we do. We strive to provide exceptional service at every touchpoint, from browsing our website to after-sales support.</p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="h-12 w-12 bg-klintblue rounded-full flex items-center justify-center text-white mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h.5A2.5 2.5 0 0020 5.5v-1.65" />
              </svg>
            </div>
            <h3 className="text-xl font-bold mb-2">Integrity</h3>
            <p>We believe in conducting business with honesty and transparency. From clear pricing to accurate product descriptions, we're committed to building trust with our customers.</p>
          </div>
        </div>
      </div>
      
      {/* Team Section */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Our Team</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Team Member 1 */}
          <div className="text-center">
            <div className="h-48 w-48 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
              <span className="text-gray-500">Photo</span>
            </div>
            <h3 className="text-xl font-bold">John Doe</h3>
            <p className="text-gray-600">CEO & Founder</p>
          </div>
          
          {/* Team Member 2 */}
          <div className="text-center">
            <div className="h-48 w-48 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
              <span className="text-gray-500">Photo</span>
            </div>
            <h3 className="text-xl font-bold">Jane Smith</h3>
            <p className="text-gray-600">COO</p>
          </div>
          
          {/* Team Member 3 */}
          <div className="text-center">
            <div className="h-48 w-48 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
              <span className="text-gray-500">Photo</span>
            </div>
            <h3 className="text-xl font-bold">Michael Johnson</h3>
            <p className="text-gray-600">CTO</p>
          </div>
          
          {/* Team Member 4 */}
          <div className="text-center">
            <div className="h-48 w-48 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
              <span className="text-gray-500">Photo</span>
            </div>
            <h3 className="text-xl font-bold">Sarah Williams</h3>
            <p className="text-gray-600">Head of Customer Service</p>
          </div>
        </div>
      </div>
      
      {/* CTA Section */}
      <div className="bg-gray-100 rounded-lg p-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Ready to Start Shopping?</h2>
        <p className="mb-6 max-w-2xl mx-auto">
          Explore our wide range of products and experience the Klint difference for yourself. We're confident you'll love what we have to offer.
        </p>
        <Link to="/products" className="btn-primary inline-block">
          Shop Now
        </Link>
        <span className="mx-4">or</span>
        <Link to="/contact" className="btn-secondary inline-block">
          Contact Us
        </Link>
      </div>
    </div>
  );
}

export default About;
