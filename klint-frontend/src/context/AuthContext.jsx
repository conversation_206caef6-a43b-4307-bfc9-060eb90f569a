import React, { createContext, useState, useContext, useEffect } from "react";
import api from "../services/api";

// Create the context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => {
  return useContext(AuthContext);
};

// Provider component
export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Check if user is already logged in on mount
  useEffect(() => {
    const token = localStorage.getItem("authToken");
    if (token) {
      // Fetch user profile
      fetchUserProfile();
    } else {
      setLoading(false);
    }
  }, []);

  // Fetch user profile
  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      const response = await api.auth.getProfile();
      // The backend returns { success: true, user: {...} }
      if (response && response.success && response.user) {
        setCurrentUser(response.user);
        setError(null);
      } else {
        throw new Error("Invalid response format");
      }
    } catch (err) {
      console.error("Error fetching user profile:", err);
      // If there's an error, clear the token
      localStorage.removeItem("authToken");
      setCurrentUser(null);
      setError("Session expired. Please log in again.");
    } finally {
      setLoading(false);
    }
  };

  // Login function
  const login = async (email, password) => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.auth.login({ email, password });

      // Save token to localStorage
      if (response.token) {
        localStorage.setItem("authToken", response.token);

        // Fetch user profile after successful login
        await fetchUserProfile();

        return { success: true };
      } else {
        throw new Error("No token received");
      }
    } catch (err) {
      setError(
        err.message || "Failed to login. Please check your credentials."
      );
      return { success: false, error: err.message || "Failed to login" };
    } finally {
      setLoading(false);
    }
  };

  // Register function
  const register = async (userData) => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.auth.register(userData);

      // Save token to localStorage if registration also logs the user in
      if (response.token) {
        localStorage.setItem("authToken", response.token);

        // Fetch user profile after successful registration
        await fetchUserProfile();
      }

      return { success: true };
    } catch (err) {
      setError(err.message || "Failed to register. Please try again.");
      return { success: false, error: err.message || "Failed to register" };
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setLoading(true);

      // Call logout API if needed
      await api.auth.logout();

      // Clear local storage and state
      localStorage.removeItem("authToken");
      setCurrentUser(null);

      return { success: true };
    } catch (err) {
      setError(err.message || "Failed to logout");
      return { success: false, error: err.message || "Failed to logout" };
    } finally {
      setLoading(false);
    }
  };

  // Context value
  const value = {
    currentUser,
    loading,
    error,
    login,
    register,
    logout,
    fetchUserProfile, // Expose this function to allow refreshing user data
    isAuthenticated: !!currentUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthContext;
