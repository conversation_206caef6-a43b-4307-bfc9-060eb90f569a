const express = require("express");
const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");
const { createOrder, getOrdersByUser, getOrderById } = require("../queries");

// Create a new order
router.post("/", authMiddleware(["client"]), async (req, res) => {
  try {
    const { shipping_address, payment_method } = req.body;

    if (!shipping_address) {
      return res.status(400).json({
        success: false,
        message: "Shipping address is required",
      });
    }

    if (!payment_method) {
      return res.status(400).json({
        success: false,
        message: "Payment method is required",
      });
    }

    const order = await createOrder(req.user.userId, {
      shipping_address,
      payment_method,
    });

    res.status(201).json({
      success: true,
      message: "Order created successfully",
      order,
    });
  } catch (error) {
    console.error("Error creating order:", error);

    if (error.message === "Cart not found") {
      return res.status(404).json({
        success: false,
        message: error.message,
      });
    }

    if (error.message === "Cart is empty") {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }

    if (error.message.includes("Insufficient stock")) {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      message: "Error creating order",
      error: error.message,
    });
  }
});

// Get all orders for the current user
router.get("/", authMiddleware(["client"]), async (req, res) => {
  try {
    const orders = await getOrdersByUser(req.user.userId);

    res.json({
      success: true,
      orders,
    });
  } catch (error) {
    console.error("Error getting orders:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving orders",
      error: error.message,
    });
  }
});

// Get a specific order by ID
router.get("/:orderId", authMiddleware(["client"]), async (req, res) => {
  try {
    const { orderId } = req.params;

    if (!orderId) {
      return res.status(400).json({
        success: false,
        message: "Order ID is required",
      });
    }

    const order = await getOrderById(orderId, req.user.userId);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    res.json({
      success: true,
      order,
    });
  } catch (error) {
    console.error("Error getting order:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving order",
      error: error.message,
    });
  }
});

module.exports = router;
