const mysql = require("mysql2/promise");
require("dotenv").config();

// Enhanced connection configuration
const dbConfig = {
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  waitForConnections: true,
  connectionLimit: 5,
  queueLimit: 0,
  enableKeepAlive: true,
  keepAliveInitialDelay: 0,
  connectTimeout: 30000,
  multipleStatements: true,
};

// Log configuration for debugging (excluding sensitive password)
console.log("Database configuration:", {
  host: dbConfig.host,
  port: dbConfig.port,
  user: dbConfig.user,
  database: dbConfig.database,
  connectionLimit: dbConfig.connectionLimit,
});

// Create a connection pool
const pool = mysql.createPool(dbConfig);

// Test database connection
const testConnection = async () => {
  try {
    const [result] = await pool.query("SELECT 1 AS test");
    console.log("Database connection successful:", result[0]);
  } catch (err) {
    console.error("Database connection error:", {
      code: err.code,
      message: err.message,
      stack: err.stack,
    });
  }
};

// Run the test connection
testConnection();

// Enhanced connection testing with initial query
pool.getConnection((err, connection) => {
  if (err) {
    console.error("Initial Database Connection Error:", {
      code: err.code,
      message: err.message,
      stack: err.stack,
      config: {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        user: process.env.DB_USER,
        database: process.env.DB_NAME,
      },
    });

    switch (err.code) {
      case "PROTOCOL_CONNECTION_LOST":
        console.error("Database connection was closed.");
        break;
      case "ER_CON_COUNT_ERROR":
        console.error("Database has too many connections.");
        break;
      case "ECONNREFUSED":
        console.error("Database connection was refused.");
        break;
      case "ER_ACCESS_DENIED_ERROR":
        console.error("Access denied for user");
        break;
      default:
        console.error("Unhandled database error:", err.code);
    }
  } else {
    // Test query to verify connection
    connection.query("SELECT 1 AS test", (queryErr, results) => {
      if (queryErr) {
        console.error("Database test query failed:", {
          code: queryErr.code,
          message: queryErr.message,
        });
      } else {
        console.log("Successfully connected to the database:", {
          result: results[0].test,
          timestamp: new Date().toISOString(),
        });
      }
      connection.release();
    });
  }
});

// Pool error handling
pool.on("error", (err) => {
  console.error("Unexpected database pool error:", {
    code: err.code,
    message: err.message,
    fatal: err.fatal,
    timestamp: new Date().toISOString(),
  });
});

// Handle connection acquisition errors
pool.on("acquire", (connection) => {
  console.log("Database connection acquired:", {
    threadId: connection.threadId,
    timestamp: new Date().toISOString(),
  });
});

pool.on("release", (connection) => {
  console.log("Database connection released:", {
    threadId: connection.threadId,
    timestamp: new Date().toISOString(),
  });
});

// Export the pool
module.exports = pool;
