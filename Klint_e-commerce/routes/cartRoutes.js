const express = require("express");
const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");
const { addToCart, getCart, removeFromCart } = require("../queries");

// Get cart contents
router.get("/", authMiddleware(["client"]), async (req, res) => {
  try {
    const cart = await getCart(req.user.userId);
    res.json({
      success: true,
      cart,
    });
  } catch (error) {
    console.error("Error getting cart:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving cart",
      error: error.message,
    });
  }
});

// Add item to cart
router.post("/add", authMiddleware(["client"]), async (req, res) => {
  try {
    const { product_id, quantity = 1 } = req.body;

    if (!product_id) {
      return res.status(400).json({
        success: false,
        message: "Product ID is required",
      });
    }

    if (quantity < 1) {
      return res.status(400).json({
        success: false,
        message: "Quantity must be at least 1",
      });
    }

    const cart = await addToCart(req.user.userId, product_id, quantity);

    res.json({
      success: true,
      message: "Item added to cart successfully",
      cart,
    });
  } catch (error) {
    console.error("Error adding item to cart:", error);

    if (error.message === "Product not found or not available") {
      return res.status(404).json({
        success: false,
        message: error.message,
      });
    }

    if (error.message === "Insufficient stock quantity") {
      return res.status(400).json({
        success: false,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      message: "Error adding item to cart",
      error: error.message,
    });
  }
});

// Remove item from cart
router.delete(
  "/remove/:productId",
  authMiddleware(["client"]),
  async (req, res) => {
    try {
      const { productId } = req.params;

      if (!productId) {
        return res.status(400).json({
          success: false,
          message: "Product ID is required",
        });
      }

      const cart = await removeFromCart(req.user.userId, productId);

      res.json({
        success: true,
        message: "Item removed from cart successfully",
        cart,
      });
    } catch (error) {
      console.error("Error removing item from cart:", error);

      if (error.message === "Cart not found") {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message === "Product not found in cart") {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: "Error removing item from cart",
        error: error.message,
      });
    }
  }
);

module.exports = router;
