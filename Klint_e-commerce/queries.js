const pool = require("./config/db");

// User queries
const createUser = async (
  username,
  email,
  passwordHash,
  role = "client",
  first_name = null,
  last_name = null,
  phone = null,
  address = null
) => {
  let conn;
  try {
    conn = await pool.getConnection();
    await conn.beginTransaction();

    const [result] = await conn.query(
      "INSERT INTO users (username, email, password, role, first_name, last_name, phone, address) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
      [
        username,
        email,
        passwordHash,
        role,
        first_name,
        last_name,
        phone,
        address,
      ]
    );

    const [user] = await conn.query(
      "SELECT id, username, email, role, first_name, last_name, phone, address, created_at FROM users WHERE id = ?",
      [result.insertId]
    );

    await conn.commit();
    return user[0];
  } catch (error) {
    if (conn) await conn.rollback();

    if (error.code === "ER_DUP_ENTRY") {
      if (error.message.includes("username")) {
        throw new Error("Username is already taken");
      }
      if (error.message.includes("email")) {
        throw new Error("Email is already registered");
      }
    }
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const findUserByEmail = async (email) => {
  let conn;
  try {
    conn = await pool.getConnection();
    const [rows] = await conn.query(
      "SELECT id, username, email, password, role, first_name, last_name, phone, address, created_at FROM users WHERE email = ?",
      [email]
    );
    return rows[0];
  } catch (error) {
    console.error("Database error in findUserByEmail:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const findUserById = async (id) => {
  let conn;
  try {
    conn = await pool.getConnection();
    const [rows] = await conn.query(
      "SELECT id, username, email, role, first_name, last_name, phone, address, created_at FROM users WHERE id = ?",
      [id]
    );
    return rows[0];
  } catch (error) {
    console.error("Database error in findUserById:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const updateUser = async (id, userData) => {
  let conn;
  try {
    conn = await pool.getConnection();
    await conn.beginTransaction();

    const { username, email, first_name, last_name, phone, address } = userData;

    await conn.query(
      "UPDATE users SET username = ?, email = ?, first_name = ?, last_name = ?, phone = ?, address = ? WHERE id = ?",
      [username, email, first_name, last_name, phone, address, id]
    );

    const [updatedUser] = await conn.query(
      "SELECT id, username, email, role, first_name, last_name, phone, address, created_at FROM users WHERE id = ?",
      [id]
    );

    await conn.commit();
    return updatedUser[0];
  } catch (error) {
    if (conn) await conn.rollback();

    if (error.code === "ER_DUP_ENTRY") {
      if (error.message.includes("username")) {
        throw new Error("Username is already taken");
      }
      if (error.message.includes("email")) {
        throw new Error("Email is already registered");
      }
    }
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

// Product queries
const createProduct = async (vendorId, productData) => {
  let conn;
  try {
    conn = await pool.getConnection();
    await conn.beginTransaction();

    const {
      name,
      description,
      price,
      stock_quantity,
      sku,
      category_id,
      is_active,
    } = productData;

    // Insert product
    const [result] = await conn.query(
      "INSERT INTO products (vendor_id, category_id, name, description, price, stock_quantity, sku, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
      [
        vendorId,
        category_id,
        name,
        description,
        price,
        stock_quantity,
        sku,
        is_active !== undefined ? is_active : true,
      ]
    );

    const productId = result.insertId;

    // Insert product attributes if provided
    if (productData.attributes && Array.isArray(productData.attributes)) {
      for (const attr of productData.attributes) {
        await conn.query(
          "INSERT INTO product_attributes (product_id, name, value) VALUES (?, ?, ?)",
          [productId, attr.name, attr.value]
        );
      }
    }

    // Insert product images if provided
    if (productData.images && Array.isArray(productData.images)) {
      for (const image of productData.images) {
        await conn.query(
          "INSERT INTO product_images (product_id, image_url, is_primary) VALUES (?, ?, ?)",
          [productId, image.url, image.is_primary]
        );
      }
    }

    // Get the complete product with attributes and images
    const [product] = await conn.query(
      `SELECT p.*,
        GROUP_CONCAT(DISTINCT CONCAT(pa.name, ':', pa.value)) as attributes,
        GROUP_CONCAT(DISTINCT CONCAT(pi.image_url, ':', pi.is_primary)) as images
      FROM products p
      LEFT JOIN product_attributes pa ON p.id = pa.product_id
      LEFT JOIN product_images pi ON p.id = pi.product_id
      WHERE p.id = ?
      GROUP BY p.id`,
      [productId]
    );

    await conn.commit();
    return product[0];
  } catch (error) {
    if (conn) await conn.rollback();

    if (error.code === "ER_DUP_ENTRY") {
      if (error.message.includes("sku")) {
        throw new Error("SKU is already in use");
      }
    }
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const getProductById = async (productId) => {
  let conn;
  try {
    conn = await pool.getConnection();

    // Get product details
    const [product] = await conn.query(
      `SELECT p.*,
        GROUP_CONCAT(DISTINCT CONCAT(pa.name, ':', pa.value)) as attributes,
        GROUP_CONCAT(DISTINCT CONCAT(pi.image_url, '||', pi.is_primary)) as images
      FROM products p
      LEFT JOIN product_attributes pa ON p.id = pa.product_id
      LEFT JOIN product_images pi ON p.id = pi.product_id
      WHERE p.id = ?
      GROUP BY p.id`,
      [productId]
    );

    if (!product[0]) {
      return null;
    }

    // Format attributes and images
    const formattedProduct = { ...product[0] };

    // Format attributes
    if (formattedProduct.attributes) {
      formattedProduct.attributes = formattedProduct.attributes
        .split(",")
        .map((attr) => {
          const [name, value] = attr.split(":");
          return { name, value };
        });
    } else {
      formattedProduct.attributes = [];
    }

    // Format images
    if (formattedProduct.images) {
      formattedProduct.images = formattedProduct.images
        .split(",")
        .map((img) => {
          const [imageUrl, isPrimary] = img.split("||");
          // Use 'url' property to match what frontend expects
          return { url: imageUrl, is_primary: isPrimary === "1" };
        });
    } else {
      formattedProduct.images = [];
    }

    return formattedProduct;
  } catch (error) {
    console.error("Error getting product:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const getProductsByVendor = async (vendorId, page = 1, limit = 10) => {
  let conn;
  try {
    conn = await pool.getConnection();

    const offset = (page - 1) * limit;

    // Get total count
    const [countResult] = await conn.query(
      "SELECT COUNT(*) as total FROM products WHERE vendor_id = ?",
      [vendorId]
    );

    const total = countResult[0].total;

    // Get products with pagination
    const [products] = await conn.query(
      `SELECT p.*,
        GROUP_CONCAT(DISTINCT CONCAT(pa.name, ':', pa.value)) as attributes,
        GROUP_CONCAT(DISTINCT CONCAT(pi.image_url, '||', pi.is_primary)) as images
      FROM products p
      LEFT JOIN product_attributes pa ON p.id = pa.product_id
      LEFT JOIN product_images pi ON p.id = pi.product_id
      WHERE p.vendor_id = ?
      GROUP BY p.id
      ORDER BY p.created_at DESC
      LIMIT ? OFFSET ?`,
      [vendorId, limit, offset]
    );

    // Format attributes and images for each product
    const formattedProducts = products.map((product) => {
      const formattedProduct = { ...product };

      // Format attributes
      if (formattedProduct.attributes) {
        formattedProduct.attributes = formattedProduct.attributes
          .split(",")
          .map((attr) => {
            const [name, value] = attr.split(":");
            return { name, value };
          });
      } else {
        formattedProduct.attributes = [];
      }

      // Format images
      if (formattedProduct.images) {
        formattedProduct.images = formattedProduct.images
          .split(",")
          .map((img) => {
            const [imageUrl, isPrimary] = img.split("||");
            // Use 'url' property to match what frontend expects
            return { url: imageUrl, is_primary: isPrimary === "1" };
          });
      } else {
        formattedProduct.images = [];
      }

      return formattedProduct;
    });

    return {
      products: formattedProducts,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error("Error getting vendor products:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const updateProduct = async (productId, vendorId, productData) => {
  let conn;
  try {
    conn = await pool.getConnection();
    await conn.beginTransaction();

    // Check if product exists and belongs to vendor
    const [product] = await conn.query(
      "SELECT id FROM products WHERE id = ? AND vendor_id = ?",
      [productId, vendorId]
    );

    if (!product[0]) {
      throw new Error(
        "Product not found or you don't have permission to update it"
      );
    }

    const {
      name,
      description,
      price,
      stock_quantity,
      sku,
      category_id,
      is_active,
    } = productData;

    // Update product
    await conn.query(
      "UPDATE products SET name = ?, description = ?, price = ?, stock_quantity = ?, sku = ?, category_id = ?, is_active = ? WHERE id = ?",
      [
        name,
        description,
        price,
        stock_quantity,
        sku,
        category_id,
        is_active,
        productId,
      ]
    );

    // Update attributes
    if (productData.attributes) {
      // Delete existing attributes
      await conn.query("DELETE FROM product_attributes WHERE product_id = ?", [
        productId,
      ]);

      // Insert new attributes
      for (const attr of productData.attributes) {
        await conn.query(
          "INSERT INTO product_attributes (product_id, name, value) VALUES (?, ?, ?)",
          [productId, attr.name, attr.value]
        );
      }
    }

    // Update images
    if (productData.images) {
      // Delete existing images
      await conn.query("DELETE FROM product_images WHERE product_id = ?", [
        productId,
      ]);

      // Insert new images
      for (const image of productData.images) {
        await conn.query(
          "INSERT INTO product_images (product_id, image_url, is_primary) VALUES (?, ?, ?)",
          [productId, image.url, image.is_primary]
        );
      }
    }

    // Get updated product
    const [updatedProduct] = await conn.query(
      `SELECT p.*,
        GROUP_CONCAT(DISTINCT CONCAT(pa.name, ':', pa.value)) as attributes,
        GROUP_CONCAT(DISTINCT CONCAT(pi.image_url, ':', pi.is_primary)) as images
      FROM products p
      LEFT JOIN product_attributes pa ON p.id = pa.product_id
      LEFT JOIN product_images pi ON p.id = pi.product_id
      WHERE p.id = ?
      GROUP BY p.id`,
      [productId]
    );

    await conn.commit();
    return updatedProduct[0];
  } catch (error) {
    if (conn) await conn.rollback();

    if (error.code === "ER_DUP_ENTRY") {
      if (error.message.includes("sku")) {
        throw new Error("SKU is already in use");
      }
    }
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const deleteProduct = async (productId, vendorId) => {
  let conn;
  try {
    conn = await pool.getConnection();
    await conn.beginTransaction();

    // Check if product exists and belongs to vendor
    const [existingProduct] = await conn.query(
      "SELECT id FROM products WHERE id = ? AND vendor_id = ?",
      [productId, vendorId]
    );

    if (!existingProduct[0]) {
      throw new Error(
        "Product not found or you don't have permission to delete it"
      );
    }

    // Delete product (cascade will handle attributes and images)
    await conn.query("DELETE FROM products WHERE id = ? AND vendor_id = ?", [
      productId,
      vendorId,
    ]);

    await conn.commit();
    return true;
  } catch (error) {
    if (conn) await conn.rollback();
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

// Category queries
const createCategory = async (categoryData) => {
  let conn;
  try {
    conn = await pool.getConnection();

    const { name, description, parent_id } = categoryData;

    const [result] = await conn.query(
      "INSERT INTO categories (name, description, parent_id) VALUES (?, ?, ?)",
      [name, description, parent_id]
    );

    const [category] = await conn.query(
      "SELECT * FROM categories WHERE id = ?",
      [result.insertId]
    );

    return category[0];
  } catch (error) {
    console.error("Error creating category:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const getCategories = async () => {
  let conn;
  try {
    conn = await pool.getConnection();

    const [categories] = await conn.query(
      "SELECT * FROM categories ORDER BY name"
    );

    return categories;
  } catch (error) {
    console.error("Error getting categories:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

// Get products for catalog
const getProductsForCatalog = async () => {
  let conn;
  try {
    conn = await pool.getConnection();

    // Simple query to get all active products with their basic info
    const query = `
      SELECT p.*,
        GROUP_CONCAT(DISTINCT CONCAT(pa.name, ':', pa.value)) as attributes,
        GROUP_CONCAT(DISTINCT CONCAT(pi.image_url, '||', pi.is_primary)) as images,
        c.name as category_name
      FROM products p
      LEFT JOIN product_attributes pa ON p.id = pa.product_id
      LEFT JOIN product_images pi ON p.id = pi.product_id
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_active = TRUE
      GROUP BY p.id
      ORDER BY p.created_at DESC
    `;

    const [products] = await conn.query(query);

    // Format the products
    const formattedProducts = products.map((product) => {
      const formattedProduct = { ...product };

      // Format attributes
      if (formattedProduct.attributes) {
        formattedProduct.attributes = formattedProduct.attributes
          .split(",")
          .map((attr) => {
            const [name, value] = attr.split(":");
            return { name, value };
          });
      } else {
        formattedProduct.attributes = [];
      }

      // Format images
      if (formattedProduct.images) {
        formattedProduct.images = formattedProduct.images
          .split(",")
          .map((img) => {
            const [imageUrl, isPrimary] = img.split("||");
            // Use 'url' property to match what frontend expects
            return { url: imageUrl, is_primary: isPrimary === "1" };
          });
      } else {
        formattedProduct.images = [];
      }

      return formattedProduct;
    });

    return formattedProducts;
  } catch (error) {
    console.error("Error getting products for catalog:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

// Get products with vendor information for price comparison
const getProductsForComparison = async (productName) => {
  let conn;
  try {
    conn = await pool.getConnection();

    // Query to get products with similar names and their vendor information
    const query = `
      SELECT p.*,
        u.username as vendor_name,
        u.email as vendor_email,
        c.name as category_name
      FROM products p
      JOIN users u ON p.vendor_id = u.id
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_active = TRUE
      AND (
        p.name LIKE ?
        OR p.description LIKE ?
      )
      ORDER BY p.price ASC
    `;

    const searchTerm = `%${productName}%`;
    const [products] = await conn.query(query, [searchTerm, searchTerm]);

    return products;
  } catch (error) {
    console.error("Error getting products for comparison:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

// Get products by category
const getProductsByCategory = async (categoryId) => {
  let conn;
  try {
    conn = await pool.getConnection();

    // Query to get products in a specific category
    const query = `
      SELECT p.*,
        GROUP_CONCAT(DISTINCT CONCAT(pa.name, ':', pa.value)) as attributes,
        GROUP_CONCAT(DISTINCT CONCAT(pi.image_url, '||', pi.is_primary)) as images,
        c.name as category_name,
        u.username as vendor_name
      FROM products p
      LEFT JOIN product_attributes pa ON p.id = pa.product_id
      LEFT JOIN product_images pi ON p.id = pi.product_id
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN users u ON p.vendor_id = u.id
      WHERE p.is_active = TRUE
      AND p.category_id = ?
      GROUP BY p.id
      ORDER BY p.created_at DESC
    `;

    const [products] = await conn.query(query, [categoryId]);

    // Format the products
    const formattedProducts = products.map((product) => {
      const formattedProduct = { ...product };

      // Format attributes
      if (formattedProduct.attributes) {
        formattedProduct.attributes = formattedProduct.attributes
          .split(",")
          .map((attr) => {
            const [name, value] = attr.split(":");
            return { name, value };
          });
      } else {
        formattedProduct.attributes = [];
      }

      // Format images
      if (formattedProduct.images) {
        formattedProduct.images = formattedProduct.images
          .split(",")
          .map((img) => {
            const [imageUrl, isPrimary] = img.split("||");
            // Use 'url' property to match what frontend expects
            return { url: imageUrl, is_primary: isPrimary === "1" };
          });
      } else {
        formattedProduct.images = [];
      }

      return formattedProduct;
    });

    return formattedProducts;
  } catch (error) {
    console.error("Error getting products by category:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

// Cart queries
const getOrCreateCart = async (userId) => {
  let conn;
  try {
    conn = await pool.getConnection();
    await conn.beginTransaction();

    // Try to get existing cart
    let [cart] = await conn.query("SELECT * FROM carts WHERE user_id = ?", [
      userId,
    ]);

    // If no cart exists, create one
    if (!cart[0]) {
      const [result] = await conn.query(
        "INSERT INTO carts (user_id) VALUES (?)",
        [userId]
      );
      [cart] = await conn.query("SELECT * FROM carts WHERE id = ?", [
        result.insertId,
      ]);
    }

    await conn.commit();
    return cart[0];
  } catch (error) {
    if (conn) await conn.rollback();
    console.error("Error in getOrCreateCart:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const addToCart = async (userId, productId, quantity = 1) => {
  let conn;
  try {
    conn = await pool.getConnection();
    await conn.beginTransaction();

    // Get or create cart
    const cart = await getOrCreateCart(userId);

    // Check if product exists and is active
    const [product] = await conn.query(
      "SELECT id, stock_quantity FROM products WHERE id = ? AND is_active = TRUE",
      [productId]
    );

    if (!product[0]) {
      throw new Error("Product not found or not available");
    }

    if (product[0].stock_quantity < quantity) {
      throw new Error("Insufficient stock quantity");
    }

    // Check if product is already in cart
    const [existingItem] = await conn.query(
      "SELECT id, quantity FROM cart_items WHERE cart_id = ? AND product_id = ?",
      [cart.id, productId]
    );

    if (existingItem[0]) {
      // Update quantity if product already in cart
      const newQuantity = existingItem[0].quantity + quantity;
      if (newQuantity > product[0].stock_quantity) {
        throw new Error("Insufficient stock quantity");
      }

      await conn.query("UPDATE cart_items SET quantity = ? WHERE id = ?", [
        newQuantity,
        existingItem[0].id,
      ]);
    } else {
      // Add new item to cart
      await conn.query(
        "INSERT INTO cart_items (cart_id, product_id, quantity) VALUES (?, ?, ?)",
        [cart.id, productId, quantity]
      );
    }

    // Get updated cart items
    const [cartItems] = await conn.query(
      `SELECT ci.*, p.name, p.price, p.stock_quantity,
        GROUP_CONCAT(DISTINCT CONCAT(pi.image_url, ':', pi.is_primary)) as images
      FROM cart_items ci
      JOIN products p ON ci.product_id = p.id
      LEFT JOIN product_images pi ON p.id = pi.product_id
      WHERE ci.cart_id = ?
      GROUP BY ci.id`,
      [cart.id]
    );

    await conn.commit();

    // Format cart items
    const formattedItems = cartItems.map((item) => {
      const formattedItem = { ...item };
      if (formattedItem.images) {
        formattedItem.images = formattedItem.images.split(",").map((img) => {
          const [imageUrl, isPrimary] = img.split("||");
          // Use 'url' property to match what frontend expects
          return { url: imageUrl, is_primary: isPrimary === "1" };
        });
      } else {
        formattedItem.images = [];
      }
      return formattedItem;
    });

    return {
      cart_id: cart.id,
      items: formattedItems,
    };
  } catch (error) {
    if (conn) await conn.rollback();
    console.error("Error in addToCart:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const getCart = async (userId) => {
  let conn;
  try {
    conn = await pool.getConnection();

    // Get cart
    const [cart] = await conn.query("SELECT * FROM carts WHERE user_id = ?", [
      userId,
    ]);

    if (!cart[0]) {
      return { cart_id: null, items: [] };
    }

    // Get cart items with product details
    const [cartItems] = await conn.query(
      `SELECT ci.*, p.name, p.price, p.stock_quantity,
        GROUP_CONCAT(DISTINCT CONCAT(pi.image_url, '||', pi.is_primary)) as images
      FROM cart_items ci
      JOIN products p ON ci.product_id = p.id
      LEFT JOIN product_images pi ON p.id = pi.product_id
      WHERE ci.cart_id = ?
      GROUP BY ci.id`,
      [cart[0].id]
    );

    // Format cart items
    const formattedItems = cartItems.map((item) => {
      const formattedItem = { ...item };
      if (formattedItem.images) {
        formattedItem.images = formattedItem.images.split(",").map((img) => {
          const [imageUrl, isPrimary] = img.split("||");
          // Use 'url' property to match what frontend expects
          return { url: imageUrl, is_primary: isPrimary === "1" };
        });
      } else {
        formattedItem.images = [];
      }
      return formattedItem;
    });

    return {
      cart_id: cart[0].id,
      items: formattedItems,
    };
  } catch (error) {
    console.error("Error in getCart:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const removeFromCart = async (userId, productId) => {
  let conn;
  try {
    conn = await pool.getConnection();
    await conn.beginTransaction();

    // Get cart
    const [cart] = await conn.query("SELECT * FROM carts WHERE user_id = ?", [
      userId,
    ]);

    if (!cart[0]) {
      throw new Error("Cart not found");
    }

    // Remove product from cart
    const [result] = await conn.query(
      "DELETE FROM cart_items WHERE cart_id = ? AND product_id = ?",
      [cart[0].id, productId]
    );

    if (result.affectedRows === 0) {
      throw new Error("Product not found in cart");
    }

    // Get updated cart items
    const [cartItems] = await conn.query(
      `SELECT ci.*, p.name, p.price, p.stock_quantity,
        GROUP_CONCAT(DISTINCT CONCAT(pi.image_url, '||', pi.is_primary)) as images
      FROM cart_items ci
      JOIN products p ON ci.product_id = p.id
      LEFT JOIN product_images pi ON p.id = pi.product_id
      WHERE ci.cart_id = ?
      GROUP BY ci.id`,
      [cart[0].id]
    );

    await conn.commit();

    // Format cart items
    const formattedItems = cartItems.map((item) => {
      const formattedItem = { ...item };
      if (formattedItem.images) {
        formattedItem.images = formattedItem.images.split(",").map((img) => {
          const [imageUrl, isPrimary] = img.split("||");
          // Use 'url' property to match what frontend expects
          return { url: imageUrl, is_primary: isPrimary === "1" };
        });
      } else {
        formattedItem.images = [];
      }
      return formattedItem;
    });

    return {
      cart_id: cart[0].id,
      items: formattedItems,
    };
  } catch (error) {
    if (conn) await conn.rollback();
    console.error("Error in removeFromCart:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

// Wishlist queries
const getOrCreateWishlist = async (userId) => {
  let conn;
  try {
    conn = await pool.getConnection();
    await conn.beginTransaction();

    // Try to get existing wishlist
    let [wishlist] = await conn.query(
      "SELECT * FROM wishlists WHERE user_id = ?",
      [userId]
    );

    // If no wishlist exists, create one
    if (!wishlist[0]) {
      const [result] = await conn.query(
        "INSERT INTO wishlists (user_id) VALUES (?)",
        [userId]
      );
      [wishlist] = await conn.query("SELECT * FROM wishlists WHERE id = ?", [
        result.insertId,
      ]);
    }

    await conn.commit();
    return wishlist[0];
  } catch (error) {
    if (conn) await conn.rollback();
    console.error("Error in getOrCreateWishlist:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const addToWishlist = async (userId, productId) => {
  let conn;
  try {
    conn = await pool.getConnection();
    await conn.beginTransaction();

    // Get or create wishlist
    const wishlist = await getOrCreateWishlist(userId);

    // Check if product exists and is active
    const [product] = await conn.query(
      "SELECT id FROM products WHERE id = ? AND is_active = TRUE",
      [productId]
    );

    if (!product[0]) {
      throw new Error("Product not found or not available");
    }

    // Check if product is already in wishlist
    const [existingItem] = await conn.query(
      "SELECT id FROM wishlist_items WHERE wishlist_id = ? AND product_id = ?",
      [wishlist.id, productId]
    );

    if (existingItem[0]) {
      throw new Error("Product is already in wishlist");
    }

    // Add product to wishlist
    await conn.query(
      "INSERT INTO wishlist_items (wishlist_id, product_id) VALUES (?, ?)",
      [wishlist.id, productId]
    );

    // Get updated wishlist items
    const [wishlistItems] = await conn.query(
      `SELECT wi.*, p.name, p.price, p.stock_quantity,
        GROUP_CONCAT(DISTINCT CONCAT(pi.image_url, '||', pi.is_primary)) as images
      FROM wishlist_items wi
      JOIN products p ON wi.product_id = p.id
      LEFT JOIN product_images pi ON p.id = pi.product_id
      WHERE wi.wishlist_id = ?
      GROUP BY wi.id`,
      [wishlist.id]
    );

    await conn.commit();

    // Format wishlist items
    const formattedItems = wishlistItems.map((item) => {
      const formattedItem = { ...item };
      if (formattedItem.images) {
        formattedItem.images = formattedItem.images.split(",").map((img) => {
          const [imageUrl, isPrimary] = img.split("||");
          // Use 'url' property to match what frontend expects
          return { url: imageUrl, is_primary: isPrimary === "1" };
        });
      } else {
        formattedItem.images = [];
      }
      return formattedItem;
    });

    return {
      wishlist_id: wishlist.id,
      items: formattedItems,
    };
  } catch (error) {
    if (conn) await conn.rollback();
    console.error("Error in addToWishlist:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const removeFromWishlist = async (userId, productId) => {
  let conn;
  try {
    conn = await pool.getConnection();
    await conn.beginTransaction();

    // Get wishlist
    const [wishlist] = await conn.query(
      "SELECT id FROM wishlists WHERE user_id = ?",
      [userId]
    );

    if (!wishlist[0]) {
      throw new Error("Wishlist not found");
    }

    // Remove product from wishlist
    const [result] = await conn.query(
      "DELETE FROM wishlist_items WHERE wishlist_id = ? AND product_id = ?",
      [wishlist[0].id, productId]
    );

    if (result.affectedRows === 0) {
      throw new Error("Product not found in wishlist");
    }

    // Get updated wishlist items
    const [wishlistItems] = await conn.query(
      `SELECT wi.*, p.name, p.price, p.stock_quantity,
        GROUP_CONCAT(DISTINCT CONCAT(pi.image_url, '||', pi.is_primary)) as images
      FROM wishlist_items wi
      JOIN products p ON wi.product_id = p.id
      LEFT JOIN product_images pi ON p.id = pi.product_id
      WHERE wi.wishlist_id = ?
      GROUP BY wi.id`,
      [wishlist[0].id]
    );

    await conn.commit();

    // Format wishlist items
    const formattedItems = wishlistItems.map((item) => {
      const formattedItem = { ...item };
      if (formattedItem.images) {
        formattedItem.images = formattedItem.images.split(",").map((img) => {
          const [imageUrl, isPrimary] = img.split("||");
          // Use 'url' property to match what frontend expects
          return { url: imageUrl, is_primary: isPrimary === "1" };
        });
      } else {
        formattedItem.images = [];
      }
      return formattedItem;
    });

    return {
      wishlist_id: wishlist[0].id,
      items: formattedItems,
    };
  } catch (error) {
    if (conn) await conn.rollback();
    console.error("Error in removeFromWishlist:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const getWishlist = async (userId) => {
  let conn;
  try {
    conn = await pool.getConnection();

    // Get wishlist
    const [wishlist] = await conn.query(
      "SELECT * FROM wishlists WHERE user_id = ?",
      [userId]
    );

    if (!wishlist[0]) {
      return { wishlist_id: null, items: [] };
    }

    // Get wishlist items with product details
    const [wishlistItems] = await conn.query(
      `SELECT wi.*, p.name, p.price, p.stock_quantity,
        GROUP_CONCAT(DISTINCT CONCAT(pi.image_url, '||', pi.is_primary)) as images
      FROM wishlist_items wi
      JOIN products p ON wi.product_id = p.id
      LEFT JOIN product_images pi ON p.id = pi.product_id
      WHERE wi.wishlist_id = ?
      GROUP BY wi.id`,
      [wishlist[0].id]
    );

    // Format wishlist items
    const formattedItems = wishlistItems.map((item) => {
      const formattedItem = { ...item };
      if (formattedItem.images) {
        formattedItem.images = formattedItem.images.split(",").map((img) => {
          const [imageUrl, isPrimary] = img.split("||");
          // Use 'url' property to match what frontend expects
          return { url: imageUrl, is_primary: isPrimary === "1" };
        });
      } else {
        formattedItem.images = [];
      }
      return formattedItem;
    });

    return {
      wishlist_id: wishlist[0].id,
      items: formattedItems,
    };
  } catch (error) {
    console.error("Error in getWishlist:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

// Order queries
const createOrder = async (userId, orderData) => {
  let conn;
  try {
    conn = await pool.getConnection();
    await conn.beginTransaction();

    const { shipping_address, payment_method } = orderData;

    // Get user's cart
    const [cart] = await conn.query("SELECT * FROM carts WHERE user_id = ?", [
      userId,
    ]);

    if (!cart[0]) {
      throw new Error("Cart not found");
    }

    // Get cart items with product details
    const [cartItems] = await conn.query(
      `SELECT ci.*, p.name, p.price, p.stock_quantity
      FROM cart_items ci
      JOIN products p ON ci.product_id = p.id
      WHERE ci.cart_id = ?`,
      [cart[0].id]
    );

    if (cartItems.length === 0) {
      throw new Error("Cart is empty");
    }

    // Calculate total amount
    let totalAmount = 0;
    for (const item of cartItems) {
      totalAmount += item.price * item.quantity;
    }

    // Create order
    const [orderResult] = await conn.query(
      `INSERT INTO orders (user_id, total_amount, shipping_address, payment_method)
      VALUES (?, ?, ?, ?)`,
      [userId, totalAmount, shipping_address, payment_method]
    );

    const orderId = orderResult.insertId;

    // Create order items and update product stock
    for (const item of cartItems) {
      // Check if enough stock is available
      if (item.stock_quantity < item.quantity) {
        throw new Error(`Insufficient stock for product: ${item.name}`);
      }

      // Create order item
      await conn.query(
        `INSERT INTO order_items (order_id, product_id, quantity, price_at_time)
        VALUES (?, ?, ?, ?)`,
        [orderId, item.product_id, item.quantity, item.price]
      );

      // Update product stock
      await conn.query(
        `UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?`,
        [item.quantity, item.product_id]
      );
    }

    // Clear the cart
    await conn.query("DELETE FROM cart_items WHERE cart_id = ?", [cart[0].id]);

    // Get the created order with items
    const [order] = await conn.query(
      `SELECT o.*,
        GROUP_CONCAT(DISTINCT CONCAT(oi.product_id, ':', oi.quantity, ':', oi.price_at_time)) as items
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.order_id
      WHERE o.id = ?
      GROUP BY o.id`,
      [orderId]
    );

    await conn.commit();

    // Format order items
    const formattedOrder = { ...order[0] };
    if (formattedOrder.items) {
      formattedOrder.items = formattedOrder.items.split(",").map((item) => {
        const [productId, quantity, price] = item.split(":");
        return { product_id: productId, quantity, price_at_time: price };
      });
    } else {
      formattedOrder.items = [];
    }

    return formattedOrder;
  } catch (error) {
    if (conn) await conn.rollback();
    console.error("Error in createOrder:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const getOrdersByUser = async (userId) => {
  let conn;
  try {
    conn = await pool.getConnection();

    // Get orders with items
    const [orders] = await conn.query(
      `SELECT o.*,
        GROUP_CONCAT(DISTINCT CONCAT(oi.product_id, ':', oi.quantity, ':', oi.price_at_time)) as items
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.order_id
      WHERE o.user_id = ?
      GROUP BY o.id
      ORDER BY o.created_at DESC`,
      [userId]
    );

    // Format orders
    const formattedOrders = orders.map((order) => {
      const formattedOrder = { ...order };
      if (formattedOrder.items) {
        formattedOrder.items = formattedOrder.items.split(",").map((item) => {
          const [productId, quantity, price] = item.split(":");
          return { product_id: productId, quantity, price_at_time: price };
        });
      } else {
        formattedOrder.items = [];
      }
      return formattedOrder;
    });

    return formattedOrders;
  } catch (error) {
    console.error("Error in getOrdersByUser:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const getOrderById = async (orderId, userId) => {
  let conn;
  try {
    conn = await pool.getConnection();

    // Get order with items
    const [order] = await conn.query(
      `SELECT o.*,
        GROUP_CONCAT(DISTINCT CONCAT(oi.product_id, ':', oi.quantity, ':', oi.price_at_time)) as items
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.order_id
      WHERE o.id = ? AND o.user_id = ?
      GROUP BY o.id`,
      [orderId, userId]
    );

    if (!order[0]) {
      return null;
    }

    // Format order
    const formattedOrder = { ...order[0] };
    if (formattedOrder.items) {
      formattedOrder.items = formattedOrder.items.split(",").map((item) => {
        const [productId, quantity, price] = item.split(":");
        return { product_id: productId, quantity, price_at_time: price };
      });
    } else {
      formattedOrder.items = [];
    }

    return formattedOrder;
  } catch (error) {
    console.error("Error in getOrderById:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

// Vendor order queries
const getOrdersForVendor = async (vendorId, status = null) => {
  let conn;
  try {
    conn = await pool.getConnection();

    // Base query to get orders containing the vendor's products
    let query = `
      SELECT DISTINCT o.*,
        GROUP_CONCAT(DISTINCT CONCAT(oi.product_id, ':', oi.quantity, ':', oi.price_at_time)) as items
      FROM orders o
      JOIN order_items oi ON o.id = oi.order_id
      JOIN products p ON oi.product_id = p.id
      WHERE p.vendor_id = ?
    `;

    const queryParams = [vendorId];

    // Add status filter if provided
    if (status) {
      query += " AND o.status = ?";
      queryParams.push(status);
    }

    query += " GROUP BY o.id ORDER BY o.created_at DESC";

    const [orders] = await conn.query(query, queryParams);

    // Format orders
    const formattedOrders = orders.map((order) => {
      const formattedOrder = { ...order };
      if (formattedOrder.items) {
        formattedOrder.items = formattedOrder.items.split(",").map((item) => {
          const [productId, quantity, price] = item.split(":");
          return { product_id: productId, quantity, price_at_time: price };
        });
      } else {
        formattedOrder.items = [];
      }
      return formattedOrder;
    });

    return formattedOrders;
  } catch (error) {
    console.error("Error in getOrdersForVendor:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const getOrderDetailsForVendor = async (orderId, vendorId) => {
  let conn;
  try {
    conn = await pool.getConnection();

    // Get order details with only the vendor's products
    const [order] = await conn.query(
      `SELECT o.*,
        GROUP_CONCAT(DISTINCT CONCAT(oi.product_id, ':', oi.quantity, ':', oi.price_at_time)) as items
      FROM orders o
      JOIN order_items oi ON o.id = oi.order_id
      JOIN products p ON oi.product_id = p.id
      WHERE o.id = ? AND p.vendor_id = ?
      GROUP BY o.id`,
      [orderId, vendorId]
    );

    if (!order[0]) {
      return null;
    }

    // Format order
    const formattedOrder = { ...order[0] };
    if (formattedOrder.items) {
      formattedOrder.items = formattedOrder.items.split(",").map((item) => {
        const [productId, quantity, price] = item.split(":");
        return { product_id: productId, quantity, price_at_time: price };
      });
    } else {
      formattedOrder.items = [];
    }

    return formattedOrder;
  } catch (error) {
    console.error("Error in getOrderDetailsForVendor:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const updateOrderStatusForVendor = async (orderId, vendorId, status) => {
  let conn;
  try {
    conn = await pool.getConnection();
    await conn.beginTransaction();

    // Check if the order contains products from this vendor
    const [orderItems] = await conn.query(
      `SELECT DISTINCT o.id
      FROM orders o
      JOIN order_items oi ON o.id = oi.order_id
      JOIN products p ON oi.product_id = p.id
      WHERE o.id = ? AND p.vendor_id = ?`,
      [orderId, vendorId]
    );

    if (!orderItems[0]) {
      throw new Error("Order not found or does not contain your products");
    }

    // Update order status
    await conn.query("UPDATE orders SET status = ? WHERE id = ?", [
      status,
      orderId,
    ]);

    // Get updated order
    const [updatedOrder] = await conn.query(
      `SELECT o.*,
        GROUP_CONCAT(DISTINCT CONCAT(oi.product_id, ':', oi.quantity, ':', oi.price_at_time)) as items
      FROM orders o
      JOIN order_items oi ON o.id = oi.order_id
      JOIN products p ON oi.product_id = p.id
      WHERE o.id = ? AND p.vendor_id = ?
      GROUP BY o.id`,
      [orderId, vendorId]
    );

    await conn.commit();

    // Format order
    const formattedOrder = { ...updatedOrder[0] };
    if (formattedOrder.items) {
      formattedOrder.items = formattedOrder.items.split(",").map((item) => {
        const [productId, quantity, price] = item.split(":");
        return { product_id: productId, quantity, price_at_time: price };
      });
    } else {
      formattedOrder.items = [];
    }

    return formattedOrder;
  } catch (error) {
    if (conn) await conn.rollback();
    console.error("Error in updateOrderStatusForVendor:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

// Admin queries
const getAllUsers = async (page = 1, limit = 20, filters = {}) => {
  let conn;
  try {
    conn = await pool.getConnection();

    let whereClause = "WHERE 1=1";
    let queryParams = [];

    // Apply filters
    if (filters.role) {
      whereClause += " AND role = ?";
      queryParams.push(filters.role);
    }

    if (filters.is_active !== undefined) {
      whereClause += " AND is_active = ?";
      queryParams.push(filters.is_active);
    }

    if (filters.search) {
      whereClause +=
        " AND (username LIKE ? OR email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)";
      const searchTerm = `%${filters.search}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }

    // Get total count
    const [countResult] = await conn.query(
      `SELECT COUNT(*) as total FROM users ${whereClause}`,
      queryParams
    );
    const total = countResult[0].total;

    // Get paginated results
    const offset = (page - 1) * limit;
    const [users] = await conn.query(
      `SELECT id, username, email, role, first_name, last_name, phone, address, is_active, created_at
       FROM users ${whereClause}
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`,
      [...queryParams, limit, offset]
    );

    return {
      users,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error("Database error in getAllUsers:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const getAllProducts = async (page = 1, limit = 20, filters = {}) => {
  let conn;
  try {
    conn = await pool.getConnection();

    let whereClause = "WHERE 1=1";
    let queryParams = [];

    // Apply filters
    if (filters.vendor_id) {
      whereClause += " AND p.vendor_id = ?";
      queryParams.push(filters.vendor_id);
    }

    if (filters.category_id) {
      whereClause += " AND p.category_id = ?";
      queryParams.push(filters.category_id);
    }

    if (filters.is_active !== undefined) {
      whereClause += " AND p.is_active = ?";
      queryParams.push(filters.is_active);
    }

    if (filters.search) {
      whereClause +=
        " AND (p.name LIKE ? OR p.description LIKE ? OR p.sku LIKE ?)";
      const searchTerm = `%${filters.search}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm);
    }

    // Get total count
    const [countResult] = await conn.query(
      `SELECT COUNT(*) as total FROM products p ${whereClause}`,
      queryParams
    );
    const total = countResult[0].total;

    // Get paginated results with vendor and category info
    const offset = (page - 1) * limit;
    const [products] = await conn.query(
      `SELECT p.*,
              u.username as vendor_name,
              u.email as vendor_email,
              c.name as category_name,
              (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = TRUE LIMIT 1) as primary_image
       FROM products p
       LEFT JOIN users u ON p.vendor_id = u.id
       LEFT JOIN categories c ON p.category_id = c.id
       ${whereClause}
       ORDER BY p.created_at DESC
       LIMIT ? OFFSET ?`,
      [...queryParams, limit, offset]
    );

    return {
      products,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error("Database error in getAllProducts:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const getAllOrders = async (page = 1, limit = 20, filters = {}) => {
  let conn;
  try {
    conn = await pool.getConnection();

    let whereClause = "WHERE 1=1";
    let queryParams = [];

    // Apply filters
    if (filters.status) {
      whereClause += " AND o.status = ?";
      queryParams.push(filters.status);
    }

    if (filters.payment_status) {
      whereClause += " AND o.payment_status = ?";
      queryParams.push(filters.payment_status);
    }

    if (filters.user_id) {
      whereClause += " AND o.user_id = ?";
      queryParams.push(filters.user_id);
    }

    if (filters.date_from) {
      whereClause += " AND DATE(o.created_at) >= ?";
      queryParams.push(filters.date_from);
    }

    if (filters.date_to) {
      whereClause += " AND DATE(o.created_at) <= ?";
      queryParams.push(filters.date_to);
    }

    // Get total count
    const [countResult] = await conn.query(
      `SELECT COUNT(*) as total FROM orders o ${whereClause}`,
      queryParams
    );
    const total = countResult[0].total;

    // Get paginated results with customer info
    const offset = (page - 1) * limit;
    const [orders] = await conn.query(
      `SELECT o.*,
              u.username as customer_name,
              u.email as customer_email,
              u.first_name,
              u.last_name,
              COUNT(oi.id) as item_count
       FROM orders o
       LEFT JOIN users u ON o.user_id = u.id
       LEFT JOIN order_items oi ON o.id = oi.order_id
       ${whereClause}
       GROUP BY o.id
       ORDER BY o.created_at DESC
       LIMIT ? OFFSET ?`,
      [...queryParams, limit, offset]
    );

    return {
      orders,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error("Database error in getAllOrders:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const getAdminStats = async () => {
  let conn;
  try {
    conn = await pool.getConnection();

    // Get user statistics
    const [userStats] = await conn.query(`
      SELECT
        COUNT(*) as total_users,
        SUM(CASE WHEN role = 'client' THEN 1 ELSE 0 END) as total_clients,
        SUM(CASE WHEN role = 'vendor' THEN 1 ELSE 0 END) as total_vendors,
        SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as total_admins,
        SUM(CASE WHEN is_active = TRUE THEN 1 ELSE 0 END) as active_users,
        SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as new_users_today
      FROM users
    `);

    // Get product statistics
    const [productStats] = await conn.query(`
      SELECT
        COUNT(*) as total_products,
        SUM(CASE WHEN is_active = TRUE THEN 1 ELSE 0 END) as active_products,
        SUM(stock_quantity) as total_inventory,
        AVG(price) as average_price,
        SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as new_products_today
      FROM products
    `);

    // Get order statistics
    const [orderStats] = await conn.query(`
      SELECT
        COUNT(*) as total_orders,
        SUM(total_amount) as total_revenue,
        AVG(total_amount) as average_order_value,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
        SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered_orders,
        SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as orders_today,
        SUM(CASE WHEN DATE(created_at) = CURDATE() THEN total_amount ELSE 0 END) as revenue_today
      FROM orders
    `);

    // Get recent activity
    const [recentOrders] = await conn.query(`
      SELECT o.id, o.total_amount, o.status, o.created_at,
             u.username as customer_name
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      ORDER BY o.created_at DESC
      LIMIT 5
    `);

    const [recentUsers] = await conn.query(`
      SELECT id, username, email, role, created_at
      FROM users
      ORDER BY created_at DESC
      LIMIT 5
    `);

    return {
      users: userStats[0],
      products: productStats[0],
      orders: orderStats[0],
      recentOrders,
      recentUsers,
    };
  } catch (error) {
    console.error("Database error in getAdminStats:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const updateUserStatus = async (userId, isActive) => {
  let conn;
  try {
    conn = await pool.getConnection();

    await conn.query("UPDATE users SET is_active = ? WHERE id = ?", [
      isActive,
      userId,
    ]);

    // Return updated user
    const [user] = await conn.query(
      "SELECT id, username, email, role, first_name, last_name, phone, address, is_active, created_at FROM users WHERE id = ?",
      [userId]
    );

    return user[0];
  } catch (error) {
    console.error("Database error in updateUserStatus:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const updateCategory = async (categoryId, categoryData) => {
  let conn;
  try {
    conn = await pool.getConnection();

    const { name, description, parent_id } = categoryData;

    await conn.query(
      "UPDATE categories SET name = ?, description = ?, parent_id = ? WHERE id = ?",
      [name, description, parent_id, categoryId]
    );

    // Return updated category
    const [category] = await conn.query(
      "SELECT * FROM categories WHERE id = ?",
      [categoryId]
    );

    return category[0];
  } catch (error) {
    console.error("Database error in updateCategory:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

const deleteCategory = async (categoryId) => {
  let conn;
  try {
    conn = await pool.getConnection();

    // Check if category has products
    const [products] = await conn.query(
      "SELECT COUNT(*) as count FROM products WHERE category_id = ?",
      [categoryId]
    );

    if (products[0].count > 0) {
      throw new Error("Cannot delete category with existing products");
    }

    // Check if category has subcategories
    const [subcategories] = await conn.query(
      "SELECT COUNT(*) as count FROM categories WHERE parent_id = ?",
      [categoryId]
    );

    if (subcategories[0].count > 0) {
      throw new Error("Cannot delete category with subcategories");
    }

    await conn.query("DELETE FROM categories WHERE id = ?", [categoryId]);

    return { success: true };
  } catch (error) {
    console.error("Database error in deleteCategory:", error);
    throw error;
  } finally {
    if (conn) conn.release();
  }
};

module.exports = {
  // User exports
  createUser,
  findUserByEmail,
  findUserById,
  updateUser,

  // Product exports
  createProduct,
  getProductById,
  getProductsByVendor,
  updateProduct,
  deleteProduct,
  getProductsForCatalog,
  getProductsForComparison,
  getProductsByCategory,

  // Category exports
  createCategory,
  getCategories,

  // Cart exports
  getOrCreateCart,
  addToCart,
  getCart,
  removeFromCart,

  // Wishlist exports
  getOrCreateWishlist,
  addToWishlist,
  removeFromWishlist,
  getWishlist,

  // Order exports
  createOrder,
  getOrdersByUser,
  getOrderById,

  // Vendor order exports
  getOrdersForVendor,
  getOrderDetailsForVendor,
  updateOrderStatusForVendor,

  // Admin exports
  getAllUsers,
  getAllProducts,
  getAllOrders,
  getAdminStats,
  updateUserStatus,
  updateCategory,
  deleteCategory,
};
