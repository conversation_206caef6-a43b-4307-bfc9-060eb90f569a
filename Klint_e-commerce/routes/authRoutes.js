const express = require("express");
const bcrypt = require("bcryptjs");
const { generateToken } = require("../utils/jwtUtils");
const authMiddleware = require("../middleware/authMiddleware");
const {
  createUser,
  findUserByEmail,
  findUserById,
  updateUser,
} = require("../queries");
const router = express.Router();

// Register a new user
router.post("/register", async (req, res) => {
  const {
    username,
    email,
    password,
    role,
    first_name,
    last_name,
    phone,
    address,
  } = req.body;

  try {
    // Input validation
    if (!username || !email || !password || !role) {
      return res.status(400).json({
        success: false,
        message: "Missing required fields",
      });
    }

    // Validate role
    const validRoles = ["client", "vendor", "supplier", "admin", "staff"];
    if (!validRoles.includes(role)) {
      return res.status(400).json({
        success: false,
        message: "Invalid role. Must be one of: " + validRoles.join(", "),
      });
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create user using the query function
    const user = await createUser(
      username,
      email,
      hashedPassword,
      role,
      first_name,
      last_name,
      phone,
      address
    );

    // Generate token
    const token = generateToken(user.id, user.role, user.email);

    res.status(201).json({
      success: true,
      message: "User registered successfully",
      user,
      token,
    });
  } catch (error) {
    console.error("Registration error:", error);

    // Handle specific errors
    if (
      error.message === "Username is already taken" ||
      error.message === "Email is already registered"
    ) {
      return res.status(409).json({
        success: false,
        message: error.message,
      });
    }

    res.status(500).json({
      success: false,
      message: "Error registering user",
      error: error.message,
    });
  }
});

// Login user
router.post("/login", async (req, res) => {
  const { email, password } = req.body;

  try {
    // Input validation
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: "Email and password are required",
      });
    }

    // Find user by email using the query function
    const user = await findUserByEmail(email);

    if (!user) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      });
    }

    // Verify password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      });
    }

    // Generate token
    const token = generateToken(user.id, user.role, user.email);

    // Return user data (excluding password)
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      success: true,
      message: "Login successful",
      user: userWithoutPassword,
      token,
    });
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({
      success: false,
      message: "Error logging in",
      error: error.message,
    });
  }
});

// Get current user profile
router.get(
  "/me",
  authMiddleware(["client", "vendor", "admin"]),
  async (req, res) => {
    try {
      // Add debug logging
      console.log("Auth request user object:", req.user);

      // Use req.user.id instead of req.user.userId to match the token payload
      const user = await findUserById(req.user.userId); // Changed from req.user.id

      if (!user) {
        return res.status(404).json({
          success: false,
          message: "User not found",
        });
      }

      // Remove sensitive information
      const { password, ...userWithoutPassword } = user;

      res.json({
        success: true,
        user: userWithoutPassword,
      });
    } catch (error) {
      console.error("Error fetching user profile:", error);
      res.status(500).json({
        success: false,
        message: "Error fetching user profile",
        error: error.message,
      });
    }
  }
);

// Update user profile
router.put(
  "/profile",
  authMiddleware(["client", "vendor", "supplier", "admin", "staff"]),
  async (req, res) => {
    try {
      const { username, email, first_name, last_name, phone, address } =
        req.body;

      // Update user using the query function
      const updatedUser = await updateUser(req.user.userId, {
        username,
        email,
        first_name,
        last_name,
        phone,
        address,
      });

      res.json({
        success: true,
        message: "Profile updated successfully",
        user: updatedUser,
      });
    } catch (error) {
      console.error("Profile update error:", error);

      // Handle specific errors
      if (
        error.message === "Username is already taken" ||
        error.message === "Email is already registered"
      ) {
        return res.status(409).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: "Error updating profile",
        error: error.message,
      });
    }
  }
);

module.exports = router;
