import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

function ProtectedVendorRoute() {
  const { isAuthenticated, currentUser, loading } = useAuth();

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="container-custom py-12 flex justify-center">
        <div className="w-16 h-16 border-4 border-klintblue border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  // If not authenticated, redirect to sign in
  if (!isAuthenticated) {
    return <Navigate to="/signin" replace />;
  }

  // If authenticated but not a vendor, redirect to access denied page
  if (currentUser.role !== 'vendor') {
    return (
      <div className="container-custom py-12 text-center">
        <h1 className="text-3xl font-bold mb-4">Access Denied</h1>
        <p className="mb-6">You don't have permission to access the vendor dashboard.</p>
        <p className="text-gray-600">
          This area is restricted to vendor accounts only. If you're a vendor, please sign in with your vendor account.
        </p>
      </div>
    );
  }

  // If authenticated and is a vendor, render the protected content
  return <Outlet />;
}

export default ProtectedVendorRoute;
